<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoInboxDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoInbox" >
        <result column="id" property="id" />
        <result column="type" property="type" />
        <result column="uid" property="uid" />
        <result column="text" property="text" />
        <result column="touid" property="touid" />
        <result column="isread" property="isread" />
        <result column="value" property="value" />
        <result column="created" property="created" />
        <result column="cid" property="cid" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `type`,
        `uid`,
        `text`,
        `touid`,
        `isread`,
        `value`,
        `created`,
        `cid`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoInbox">
        INSERT INTO ${prefix}_inbox
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != touid'>
                `touid`,
            </if>
            <if test ='null != isread'>
                `isread`,
            </if>
            <if test ='null != value'>
                `value`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != cid'>
                `cid`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != touid'>
                #{touid},
            </if>
            <if test ='null != isread'>
                #{isread},
            </if>
            <if test ='null != value'>
                #{value},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != cid'>
                #{cid}
            </if>
        </trim>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoInbox">
        UPDATE ${prefix}_inbox
        <set>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != touid'>`touid` = #{touid},</if>
            <if test ='null != isread'>`isread` = #{isread},</if>
            <if test ='null != value'>`value` = #{value},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != cid'>`cid` = #{cid}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_inbox
        WHERE `id` = #{key}
    </delete>


    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_inbox
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_inbox
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != touid'>
                and `touid` = #{touid}
            </if>
            <if test ='null != isread'>
                and `isread` = #{isread}
            </if>
            <if test ='null != value'>
                and `value` = #{value}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != cid'>
                and `cid` = #{cid}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_inbox
        <where>
            <if test ='null != typechoInbox.id'>
                and `id` = #{typechoInbox.id}
            </if>
            <if test ='null != typechoInbox.type'>
                <if test ='"comment" == typechoInbox.type'>
                    and `type` IN ('comment', 'postComment')
                </if>
                <if test ='"comment" != typechoInbox.type'>
                    and `type` = #{typechoInbox.type}
                </if>

            </if>
            <if test ='null != typechoInbox.uid'>
                and `uid` = #{typechoInbox.uid}
            </if>
            <if test ='null != typechoInbox.text'>
                and `text` = #{typechoInbox.text}
            </if>
            <if test ='null != typechoInbox.touid'>
                and `touid` = #{typechoInbox.touid}
            </if>
            <if test ='null != typechoInbox.isread'>
                and `isread` = #{typechoInbox.isread}
            </if>
            <if test ='null != typechoInbox.value'>
                and `value` = #{typechoInbox.value}
            </if>
            <if test ='null != typechoInbox.created'>
                and `created` = #{typechoInbox.created}
            </if>
        </where>
        order by created desc
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_inbox
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != type'>
                <if test ='"comment" == type'>
                    and `type` IN ('comment', 'postComment')
                </if>
                <if test ='"comment" != type'>
                    and `type` = #{type}
                </if>
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != touid'>
                and `touid` = #{touid}
            </if>
            <if test ='null != isread'>
                and `isread` = #{isread}
            </if>
            <if test ='null != value'>
                and `value` = #{value}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
        </where>
    </select>
</mapper>