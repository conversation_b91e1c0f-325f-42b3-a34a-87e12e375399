# 服务器配置
server.port=7979

# slf4j日志配置，mybatis-dao包下设置为DEBUG级别，方便调试时查看debug信息
logging.level.com.StarProApi.dao=DEBUG
logging.config=classpath:logback-spring.xml
logging.file=/log/StarProApi

# mybatis配置
mybatis.mapper-locations = classpath:mapper/*Mapper.xml
mybatis.type-aliases-package = com.StarProApi.entity

# mysql配置
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.url=***********************************************************
spring.datasource.username=root
spring.datasource.password=root
# mysql数据库表前缀配置
mybatis.configuration.variables.prefix=typecho

# redis配置
spring.session.store-type=redis
spring.redis.host=127.0.0.1
spring.redis.password=
spring.redis.port=6379
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.jedis.pool.max-active=20
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.jedis.pool.max-wait=-1
# 连接池中的最大空闲连接
spring.redis.jedis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.jedis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=3000

# 邮件服务器配置，这里以QQ邮箱为例
#POP3服务器: pop.qq.com
#SMTP服务器: smtp.qq.com
#IMAP服务器: imap.qq.com
spring.mail.protocol=smtps
spring.mail.host=smtp.126.com
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.port:465
spring.mail.properties.mail.smtp.ssl.enable:true
spring.mail.default-encoding=UTF-8

webinfo.key=123456

webinfo.usertime=86400
webinfo.contentCache=6
webinfo.contentInfoCache=60
webinfo.CommentCache=20
webinfo.userCache=10

# 这里配置的是缓存的前缀，如果是API项目建议设置，如果是redis共用建议设置前缀
web.prefix=typecho

# 支付宝网关地址
gateway_url=https://openapi.alipaydev.com/gateway.do

#xss过滤配置
mica.xss.allow-tags = p, audio, video
mica.xss.allow-attributes = src, controls, controlslist, id, width, height, webkit-playsinline, playsinline, x5-playsinline

# 请求头大小限制
#server.max-http-header-size=10000000

#文件上传大小限制
spring.servlet.multipart.enabled: true
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=100MB
#spring.servlet.multipart.location=/data/tmp

# (安全考虑)允许上传的文件扩展名
upload.allowed-extensions=.jpg,.jpeg,.png,.gif,.bmp,.webp,.mp4,.avi,.mov,.zip,.rar,.7z,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.mp3,.wav,.ogg,.flac,.apk,.ipa