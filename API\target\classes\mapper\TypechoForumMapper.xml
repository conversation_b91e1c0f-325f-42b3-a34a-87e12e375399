<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoForumDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoForum" >
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="section" property="section" />
        <result column="typeid" property="typeid" />
        <result column="created" property="created" />
        <result column="modified" property="modified" />
        <result column="text" property="text" />
        <result column="authorId" property="authorId" />
        <result column="status" property="status" />
        <result column="commentsNum" property="commentsNum" />
        <result column="views" property="views" />
        <result column="likes" property="likes" />
        <result column="isTop" property="isTop" />
        <result column="isrecommend" property="isrecommend" />
        <result column="isswiper" property="isswiper" />
        <result column="replyTime" property="replyTime" />
        <result column="isMd" property="isMd" />
        <result column="sid" property="sid" />


    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `title`,
        `section`,
        `typeid`,
        `created`,
        `modified`,
        `text`,
        `authorId`,
        `status`,
        `commentsNum`,
        `views`,
        `likes`,
        `isTop`,
        `isrecommend`,
        `isswiper`,
        `replyTime`,
        `isMd`,
        `sid`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoForum"   keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ${prefix}_forum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != title'>
                `title`,
            </if>
            <if test ='null != section'>
                `section`,
            </if>
            <if test ='null != typeid'>
                `typeid`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != modified'>
                `modified`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != authorId'>
                `authorId`,
            </if>
            <if test ='null != status'>
                `status`,
            </if>
            <if test ='null != commentsNum'>
                `commentsNum`,
            </if>
            <if test ='null != views'>
                `views`,
            </if>
            <if test ='null != likes'>
                `likes`,
            </if>
            <if test ='null != isTop'>
                `isTop`,
            </if>
            <if test ='null != isrecommend'>
                `isrecommend`,
            </if>
            <if test ='null != isswiper'>
                `isswiper`,
            </if>
            <if test ='null != isMd'>
                `isMd`,
            </if>
            <if test ='null != sid'>
                `sid`
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != title'>
                #{title},
            </if>
            <if test ='null != section'>
                #{section},
            </if>
            <if test ='null != typeid'>
                #{typeid},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != modified'>
                #{modified},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != authorId'>
                #{authorId},
            </if>
            <if test ='null != status'>
                #{status},
            </if>
            <if test ='null != commentsNum'>
                #{commentsNum},
            </if>
            <if test ='null != views'>
                #{views},
            </if>
            <if test ='null != likes'>
                #{likes},
            </if>
            <if test ='null != isTop'>
                #{isTop},
            </if>
            <if test ='null != isrecommend'>
                #{isrecommend},
            </if>
            <if test ='null != isswiper'>
                #{isswiper},
            </if>
            <if test ='null != isMd'>
                #{isMd},
            </if>
            <if test ='null != sid'>
                #{sid}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_forum ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.title},
                #{curr.section},
                #{curr.typeid},
                #{curr.created},
                #{curr.modified},
                #{curr.text},
                #{curr.authorId},
                #{curr.status},
                #{curr.commentsNum},
                #{curr.views},
                #{curr.likes},
                #{curr.isTop},
                #{curr.isrecommend},
                #{curr.isswiper},
                #{curr.isMd}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoForum">
        UPDATE ${prefix}_forum
        <set>
            <if test ='null != title'>`title` = #{title},</if>
            <if test ='null != section'>`section` = #{section},</if>
            <if test ='null != typeid'>`typeid` = #{typeid},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != modified'>`modified` = #{modified},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != authorId'>`authorId` = #{authorId},</if>
            <if test ='null != status'>`status` = #{status},</if>
            <if test ='null != commentsNum'>`commentsNum` = #{commentsNum},</if>
            <if test ='null != views'>`views` = #{views},</if>
            <if test ='null != likes'>`likes` = #{likes},</if>
            <if test ='null != isTop'>`isTop` = #{isTop},</if>
            <if test ='null != isrecommend'>`isrecommend` = #{isrecommend},</if>
            <if test ='null != isswiper'>`isswiper` = #{isswiper},</if>
            <if test ='null != replyTime'>`replyTime` = #{replyTime},</if>
            <if test ='null != sid'>`sid` = #{sid}</if>

        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_forum
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_forum WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != title'>
                and `title` = #{title}
            </if>
            <if test ='null != section'>
                and `section` = #{section}
            </if>
            <if test ='null != typeid'>
                and `typeid` = #{typeid}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != modified'>
                and `modified` = #{modified}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != authorId'>
                and `authorId` = #{authorId}
            </if>
            <if test ='null != status'>
                and `status` = #{status}
            </if>
            <if test ='null != commentsNum'>
                and `commentsNum` = #{commentsNum}
            </if>
            <if test ='null != views'>
                and `views` = #{views}
            </if>
            <if test ='null != likes'>
                and `likes` = #{likes}
            </if>
            <if test ='null != isTop'>
                and `isTop` = #{isTop}
            </if>
            <if test ='null != isrecommend'>
                and `isrecommend` = #{isrecommend}
            </if>
            <if test ='null != isswiper'>
                and `isswiper` = #{isswiper}
            </if>
            <if test ='null != sid'>
                and `sid` = #{sid}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum
        <where>
            <if test ='null != typechoForum.id'>
                and `id` = #{typechoForum.id}
            </if>
            <if test ='null != typechoForum.title'>
                and `title` = #{typechoForum.title}
            </if>
            <if test ='null != typechoForum.section'>
                and `section` = #{typechoForum.section}
            </if>
            <if test ='null != typechoForum.typeid'>
                and `typeid` = #{typechoForum.typeid}
            </if>
            <if test ='null != typechoForum.created'>
                and `created` = #{typechoForum.created}
            </if>
            <if test ='null != typechoForum.modified'>
                and `modified` = #{typechoForum.modified}
            </if>
            <if test ='null != typechoForum.text'>
                and `text` = #{typechoForum.text}
            </if>
            <if test ='null != typechoForum.authorId'>
                and `authorId` = #{typechoForum.authorId}
            </if>
            <if test ='null != typechoForum.status'>
                and `status` = #{typechoForum.status}
            </if>
            <if test ='null != typechoForum.commentsNum'>
                and `commentsNum` = #{typechoForum.commentsNum}
            </if>
            <if test ='null != typechoForum.views'>
                and `views` = #{typechoForum.views}
            </if>
            <if test ='null != typechoForum.likes'>
                and `likes` = #{typechoForum.likes}
            </if>
            <if test ='null != typechoForum.isTop'>
                and `isTop` = #{typechoForum.isTop}
            </if>
            <if test ='null != typechoForum.isrecommend'>
                and `isrecommend` = #{typechoForum.isrecommend}
            </if>
            <if test ='null != typechoForum.isswiper'>
                and `isswiper` = #{typechoForum.isswiper}
            </if>
            <if test ='null != typechoForum.sid'>
                and `sid` = #{typechoForum.sid}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`title`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_forum
        <where>
            <if test ='null != typechoForum.id'>
                and `id` = #{typechoForum.id}
            </if>
            <if test ='null != typechoForum.title'>
                and `title` = #{typechoForum.title}
            </if>
            <if test ='null != typechoForum.section'>
                and `section` = #{typechoForum.section}
            </if>
            <if test ='null != typechoForum.typeid'>
                and `typeid` = #{typechoForum.typeid}
            </if>
            <if test ='null != typechoForum.created'>
                and `created` = #{typechoForum.created}
            </if>
            <if test ='null != typechoForum.modified'>
                and `modified` = #{typechoForum.modified}
            </if>
            <if test ='null != typechoForum.text'>
                and `text` = #{typechoForum.text}
            </if>
            <if test ='null != typechoForum.authorId'>
                and `authorId` = #{typechoForum.authorId}
            </if>
            <if test ='null != typechoForum.status'>
                and `status` = #{typechoForum.status}
            </if>
            <if test ='null != typechoForum.commentsNum'>
                and `commentsNum` = #{typechoForum.commentsNum}
            </if>
            <if test ='null != typechoForum.views'>
                and `views` = #{typechoForum.views}
            </if>
            <if test ='null != typechoForum.likes'>
                and `likes` = #{typechoForum.likes}
            </if>
            <if test ='null != typechoForum.isTop'>
                and `isTop` = #{typechoForum.isTop}
            </if>
            <if test ='null != typechoForum.isrecommend'>
                and `isrecommend` = #{typechoForum.isrecommend}
            </if>
            <if test ='null != typechoForum.isswiper'>
                and `isswiper` = #{typechoForum.isswiper}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`title`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
    </select>
</mapper>