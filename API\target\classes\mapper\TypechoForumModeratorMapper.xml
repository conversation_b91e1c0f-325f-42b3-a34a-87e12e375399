<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoForumModeratorDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoForumModerator" >
        <result column="id" property="id" />
        <result column="sectionId" property="sectionId" />
        <result column="uid" property="uid" />
        <result column="purview" property="purview" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `sectionId`,
        `uid`,
        `purview`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoForumModerator">
        INSERT INTO ${prefix}_forum_moderator
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != sectionId'>
                `sectionId`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != purview'>
                `purview`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != sectionId'>
                #{sectionId},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != purview'>
                #{purview}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_forum_moderator ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.sectionId},
                #{curr.uid},
                #{curr.purview}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoForumModerator">
        UPDATE ${prefix}_forum_moderator
        <set>
            <if test ='null != sectionId'>`sectionId` = #{sectionId},</if>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != purview'>`purview` = #{purview}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_forum_moderator
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_forum_moderator WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_moderator
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_moderator
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != sectionId'>
                and `sectionId` = #{sectionId}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != purview'>
                and `purview` = #{purview}
            </if>
        </where>
        order by purview desc
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_moderator
        <where>
            <if test ='null != typechoForumModerator.id'>
                and `id` = #{typechoForumModerator.id}
            </if>
            <if test ='null != typechoForumModerator.sectionId'>
                and `sectionId` = #{typechoForumModerator.sectionId}
            </if>
            <if test ='null != typechoForumModerator.uid'>
                and `uid` = #{typechoForumModerator.uid}
            </if>
            <if test ='null != typechoForumModerator.purview'>
                and `purview` = #{typechoForumModerator.purview}
            </if>
        </where>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_forum_moderator
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != sectionId'>
                and `sectionId` = #{sectionId}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != purview'>
                and `purview` = #{purview}
            </if>
        </where>
    </select>
</mapper>