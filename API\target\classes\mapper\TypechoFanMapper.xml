<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoFanDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoFan" >
        <result column="id" property="id" />
        <result column="created" property="created" />
        <result column="uid" property="uid" />
        <result column="touid" property="touid" />
        <collection property="users" ofType="com.StarProApi.entity.TypechoUsers">
            <id  property="uid"  column="touid" />
            <result column="name" property="name" />
            <result column="password" property="password" />
            <result column="mail" property="mail" />
            <result column="url" property="url" />
            <result column="screenName" property="screenName" />
            <result column="created" property="created" />
            <result column="activated" property="activated" />
            <result column="logged" property="logged" />
            <result column="group" property="groupKey" />
            <result column="authCode" property="authCode" />
            <result column="introduce" property="introduce" />
            <result column="assets" property="assets" />
            <result column="address" property="address" />
            <result column="pay" property="pay" />
            <result column="customize" property="customize" />
            <result column="avatar" property="avatar" />
            <result column="experience" property="experience" />
            <result column="clientId" property="clientId" />
            <result column="bantime" property="bantime" />
            <result column="posttime" property="posttime" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `created`,
        `uid`,
        `touid`
    </sql>
    <sql id="User_Column_List">
        f.id,
        f.created,
        f.uid,
        f.touid,
        u.posttime,
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoFan">
        INSERT INTO ${prefix}_fan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != touid'>
                `touid`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != touid'>
                #{touid}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_fan ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.created},
                #{curr.uid},
                #{curr.touid}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoFan">
        UPDATE ${prefix}_fan
        <set>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != touid'>`touid` = #{touid}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_fan
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_fan WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_fan
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_fan
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != touid'>
                and `touid` = #{touid}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_fan
        <where>
            <if test ='null != typechoFan.id'>
                and `id` = #{typechoFan.id}
            </if>
            <if test ='null != typechoFan.created'>
                and `created` = #{typechoFan.created}
            </if>
            <if test ='null != typechoFan.uid'>
                and `uid` = #{typechoFan.uid}
            </if>
            <if test ='null != typechoFan.touid'>
                and `touid` = #{typechoFan.touid}
            </if>
        </where>
        order by created desc
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 分页联表条件查询 -->
    <select id="selectUserPage" resultMap="BaseResultMap">
        SELECT <include refid="User_Column_List" />
        FROM ${prefix}_fan f inner join ${prefix}_users u on f.touid = u.uid
        <where>
            <if test ='null != typechoFan.id'>
                and f.id = #{typechoFan.id}
            </if>
            <if test ='null != typechoFan.created'>
                and f.created = #{typechoFan.created}
            </if>
            <if test ='null != typechoFan.uid'>
                and f.uid = #{typechoFan.uid}
            </if>
            <if test ='null != typechoFan.touid'>
                and f.touid = #{typechoFan.touid}
            </if>
        </where>
        order by u.posttime desc
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>


    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_fan
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != touid'>
                and `touid` = #{touid}
            </if>
        </where>
    </select>
</mapper>