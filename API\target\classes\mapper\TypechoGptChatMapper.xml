<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoGptChatDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoGptChat" >
        <result column="id" property="id" />
        <result column="gptid" property="gptid" />
        <result column="uid" property="uid" />
        <result column="sessionId" property="sessionId" />
        <result column="created" property="created" />
        <result column="replyTime" property="replyTime" />

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `gptid`,
        `uid`,
        `sessionId`,
        `created`,
        `replyTime`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoGptChat"   keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ${prefix}_gpt_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != gptid'>
                `gptid`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != sessionId'>
                `sessionId`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != replyTime'>
                `replyTime`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != gptid'>
                #{gptid},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != sessionId'>
                #{sessionId},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != replyTime'>
                #{replyTime}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_gpt_chat ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.gptid},
                #{curr.uid},
                #{curr.sessionId},
                #{curr.created}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoGptChat">
        UPDATE ${prefix}_gpt_chat
        <set>
            <if test ='null != gptid'>`gptid` = #{gptid},</if>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != sessionId'>`sessionId` = #{sessionId},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != replyTime'>`replyTime` = #{replyTime}</if>

        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_gpt_chat
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_gpt_chat WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt_chat
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt_chat
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != gptid'>
                and `gptid` = #{gptid}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != sessionId'>
                and `sessionId` = #{sessionId}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t${prefix}_gpt_chat
        <where>
            <if test ='null != typechoGptChat.id'>
                and `id` = #{typechoGptChat.id}
            </if>
            <if test ='null != typechoGptChat.gptid'>
                and `gptid` = #{typechoGptChat.gptid}
            </if>
            <if test ='null != typechoGptChat.uid'>
                and `uid` = #{typechoGptChat.uid}
            </if>
            <if test ='null != typechoGptChat.sessionId'>
                and `sessionId` = #{typechoGptChat.sessionId}
            </if>
            <if test ='null != typechoGptChat.created'>
                and `created` = #{typechoGptChat.created}
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_gpt_chat
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != gptid'>
                and `gptid` = #{gptid}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != sessionId'>
                and `sessionId` = #{sessionId}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
        </where>
    </select>
</mapper>