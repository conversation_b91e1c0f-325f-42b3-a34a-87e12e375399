<!doctype html>
<html data-n-head-ssr lang="en" data-n-head="%7B%22lang%22:%7B%22ssr%22:%22en%22%7D%7D">
<head>
    <title>烟圈API - 热爱你的热爱</title>
    <meta data-n-head="ssr" charset="utf-8">
    <meta data-n-head="ssr" name="viewport" content="width=device-width,initial-scale=1">
    <meta data-n-head="ssr" data-hid="description" name="description" content="">
    <meta data-n-head="ssr" name="format-detection" content="telephone=no">

    <!-- 移动端状态栏优化 -->
    <meta name="theme-color" content="#0f172a">
    <meta name="msapplication-navbutton-color" content="#0f172a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="烟圈API">

    <!-- Android Chrome -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="烟圈API">

    <!-- Windows Phone -->
    <meta name="msapplication-TileColor" content="#0f172a">
    <meta name="msapplication-TileImage" content="/favicon.ico">

    <!-- PWA支持 -->
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- 防止用户缩放 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link data-n-head="ssr" rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            min-height: 100dvh; /* 动态视口高度，iOS Safari友好 */
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            /* iOS Safari兼容的背景设置 */
            background-color: #0f172a;
            background-image:
                -webkit-linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%),
                -webkit-radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
                -webkit-radial-gradient(circle at 80% 20%, rgba(74, 222, 128, 0.08) 0%, transparent 50%);
            background-image:
                linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%),
                radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(74, 222, 128, 0.08) 0%, transparent 50%);
            background-size: 100% 100%, 100% 100%, 100% 100%;
            background-repeat: no-repeat;
            background-attachment: scroll; /* iOS Safari不支持fixed */
        }

        /* 桌面端增强效果 */
        @media (min-width: 768px) and (prefers-reduced-motion: no-preference) {
            body::before {
                content: '';
                position: absolute; /* 改为absolute，避免iOS Safari的fixed问题 */
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 40% 40%, rgba(22, 163, 74, 0.06) 0%, transparent 50%);
                animation: modernGlow 8s ease-in-out infinite alternate;
                z-index: -1;
                pointer-events: none;
            }
        }

        /* iOS Safari专用优化 */
        @supports (-webkit-touch-callout: none) {
            body {
                /* iOS Safari专用背景 */
                background: #0f172a;
                background: -webkit-linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
                background-size: 100% 100%;
                background-attachment: scroll;
            }

            /* 移除伪元素，直接使用多重背景 */
            body::before {
                display: none;
            }
        }

        @keyframes modernGlow {
            0% {
                opacity: 0.6;
                transform: scale(1) rotate(0deg);
            }
            100% {
                opacity: 0.9;
                transform: scale(1.05) rotate(1deg);
            }
        }

        @keyframes textLogoBreathing {
            0%, 100% {
                transform: scale(1);
                text-shadow:
                    2px 2px 4px rgba(0,0,0,0.4),
                    0 0 20px rgba(34, 197, 94, 0.4),
                    0 0 40px rgba(74, 222, 128, 0.3);
            }
            25% {
                transform: scale(1.02);
                text-shadow:
                    2px 2px 4px rgba(0,0,0,0.45),
                    0 0 25px rgba(34, 197, 94, 0.5),
                    0 0 50px rgba(74, 222, 128, 0.35);
            }
            50% {
                transform: scale(1.06);
                text-shadow:
                    3px 3px 6px rgba(0,0,0,0.6),
                    0 0 40px rgba(34, 197, 94, 0.7),
                    0 0 80px rgba(74, 222, 128, 0.5),
                    0 0 120px rgba(22, 163, 74, 0.3);
            }
            75% {
                transform: scale(1.02);
                text-shadow:
                    2px 2px 4px rgba(0,0,0,0.45),
                    0 0 25px rgba(34, 197, 94, 0.5),
                    0 0 50px rgba(74, 222, 128, 0.35);
            }
        }
        .container {
            text-align: center;
            color: white;
        }
        .index-main {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 60vh;
            padding: 40px;
            position: relative;
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 80px;
        }
        .star-logo1 {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow:
                2px 2px 4px rgba(0,0,0,0.4),
                0 0 20px rgba(34, 197, 94, 0.4),
                0 0 40px rgba(74, 222, 128, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: textLogoBreathing 3s ease-in-out infinite;
            transform-origin: center center;
            background: linear-gradient(135deg, #22c55e 0%, #4ade80 50%, #22c55e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rule-version {
            margin-top: 20px;
        }
        .rule-btn {
            margin-top: 30px;
        }
        .star-button1 {
            background: rgba(34, 197, 94, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #ffffff;
            padding: 18px 36px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-block;
            position: relative;
            overflow: hidden;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(34, 197, 94, 0.2),
                inset 0 1px 0 rgba(74, 222, 128, 0.3),
                inset 0 -1px 0 rgba(22, 163, 74, 0.2);
        }

        .star-button1::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.5s ease;
        }

        .star-button1:hover {
            background: rgba(34, 197, 94, 0.25);
            border-color: rgba(34, 197, 94, 0.5);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 16px 50px rgba(0, 0, 0, 0.4),
                0 0 40px rgba(34, 197, 94, 0.4),
                0 0 80px rgba(74, 222, 128, 0.3),
                inset 0 1px 0 rgba(74, 222, 128, 0.4),
                inset 0 -1px 0 rgba(22, 163, 74, 0.3);
        }

        .star-button1:hover::before {
            left: 100%;
        }

        .star-button1:active {
            transform: translateY(0);
        }
        
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 15, 35, 0.8);
            backdrop-filter: blur(12px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; backdrop-filter: blur(0px); }
            to { opacity: 1; backdrop-filter: blur(12px); }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                backdrop-filter: blur(25px);
            }
        }

        .modal {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(30px);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 28px;
            padding: 40px;
            width: 90%;
            max-width: 460px;
            box-shadow:
                0 30px 100px rgba(0, 0, 0, 0.5),
                0 10px 40px rgba(0, 0, 0, 0.3),
                0 0 50px rgba(34, 197, 94, 0.15),
                inset 0 1px 0 rgba(74, 222, 128, 0.3),
                inset 0 -1px 0 rgba(22, 163, 74, 0.1);
            color: #ffffff;
            position: relative;
            animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .modal-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .modal-content {
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }
        .form-input {
            width: 100%;
            padding: 16px 18px;
            background: rgba(154, 205, 50, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(154, 205, 50, 0.2);
            border-radius: 16px;
            font-size: 16px;
            color: #ffffff;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
        }
        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        .form-input:focus {
            outline: none;
            border-color: rgba(154, 205, 50, 0.4);
            background: rgba(154, 205, 50, 0.15);
            box-shadow:
                0 0 0 3px rgba(154, 205, 50, 0.1),
                0 0 20px rgba(154, 205, 50, 0.2),
                0 8px 25px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(154, 205, 50, 0.2);
            transform: translateY(-1px);
        }
        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .btn-cancel {
            background: rgba(154, 205, 50, 0.08);
            color: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(154, 205, 50, 0.15);
            backdrop-filter: blur(10px);
        }
        .btn-cancel:hover {
            background: rgba(154, 205, 50, 0.15);
            color: rgba(255, 255, 255, 0.9);
            border-color: rgba(154, 205, 50, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(154, 205, 50, 0.2);
        }
        .btn-primary {
            background: linear-gradient(135deg, rgba(154, 205, 50, 0.8) 0%, rgba(173, 255, 47, 0.8) 100%);
            color: white;
            border: 1px solid rgba(154, 205, 50, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, rgba(154, 205, 50, 0.9) 0%, rgba(173, 255, 47, 0.9) 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(154, 205, 50, 0.4), 0 0 20px rgba(173, 255, 47, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.8) 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }
        .btn-blue {
            background: linear-gradient(135deg, rgba(124, 252, 0, 0.8) 0%, rgba(154, 205, 50, 0.8) 100%);
            color: white;
            border: 1px solid rgba(154, 205, 50, 0.3);
            backdrop-filter: blur(10px);
        }
        .btn-blue:hover {
            background: linear-gradient(135deg, rgba(124, 252, 0, 0.9) 0%, rgba(154, 205, 50, 0.9) 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(124, 252, 0, 0.4), 0 0 20px rgba(154, 205, 50, 0.3);
        }
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        .success-message {
            color: #28a745;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 响应式样式 */
        @media (max-width: 480px) {
            .star-logo1 {
                font-size: 36px;
            }

            .star-button1 {
                padding: 14px 28px;
                font-size: 15px;
            }

            .index-main {
                padding: 20px;
            }

            .logo-container {
                margin-bottom: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="index-main">
            <div class="logo-container">
                <span class="star-logo1 font50">烟圈API</span>
            </div>
            <div class="rule-btn">
                <span class="star-button1" onclick="showKeyVerification()">安装/更新</span>
            </div>
        </div>
    </div>

    <!-- KEY验证弹窗 -->
    <div id="keyVerificationModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">KEY验证</div>
                <div style="color: #666; font-size: 14px;">请输入访问KEY以继续安装</div>
            </div>
            <div class="modal-content">
                <div class="form-group">
                    <label class="form-label">访问KEY</label>
                    <input type="password" id="accessKey" class="form-input" placeholder="请输入访问KEY" />
                </div>
                <div id="keyErrorMessage" class="error-message" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-cancel" onclick="hideKeyVerification()">取消</button>
                <button class="btn btn-primary" onclick="verifyKey()">验证</button>
            </div>
        </div>
    </div>

    <!-- 第一步安装弹窗 -->
    <div id="firstInstallModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">开始第一步安装</div>
            </div>
            <div class="modal-content">
                <div style="color: #666; font-size: 14px; text-align: center;">
                    确定要开始安装吗？安装会对数据库造成修改，请在安装前备份好数据！
                </div>
                <div id="installErrorMessage" class="error-message" style="display: none;"></div>
                <div id="installSuccessMessage" class="success-message" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" onclick="startInstall()">开始安装</button>
                <button class="btn btn-primary" onclick="updateOnly()">仅更新</button>
                <button class="btn btn-blue" onclick="hideFirstInstall()">再等等</button>
            </div>
        </div>
    </div>

    <!-- 第二步安装弹窗 -->
    <div id="secondInstallModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">进行第二步安装</div>
            </div>
            <div class="modal-content">
                <div style="color: #666; font-size: 14px; text-align: center;">
                    第一步安装完成，是否继续进行第二步安装？
                </div>
                <div id="secondInstallErrorMessage" class="error-message" style="display: none;"></div>
                <div id="secondInstallSuccessMessage" class="success-message" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-cancel" onclick="hideSecondInstall()">取消</button>
                <button class="btn btn-primary" onclick="startSecondInstall()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 动态设置状态栏颜色
        function updateStatusBarColor() {
            const themeColorMeta = document.querySelector('meta[name="theme-color"]');
            const msNavButtonMeta = document.querySelector('meta[name="msapplication-navbutton-color"]');
            const tileColorMeta = document.querySelector('meta[name="msapplication-TileColor"]');

            // 根据页面状态动态调整颜色
            const baseColor = '#0f172a';
            const modalColor = '#1e293b';

            // 检查是否有弹窗打开
            const isModalOpen = document.querySelector('.modal-overlay[style*="flex"]') ||
                               document.querySelector('.modal-overlay[style*="block"]');

            const currentColor = isModalOpen ? modalColor : baseColor;

            if (themeColorMeta) themeColorMeta.setAttribute('content', currentColor);
            if (msNavButtonMeta) msNavButtonMeta.setAttribute('content', currentColor);
            if (tileColorMeta) tileColorMeta.setAttribute('content', currentColor);
        }

        // 页面加载时设置状态栏颜色
        document.addEventListener('DOMContentLoaded', updateStatusBarColor);

        let verifiedKey = '';

        // 显示KEY验证弹窗
        function showKeyVerification() {
            document.getElementById('keyVerificationModal').style.display = 'flex';
            document.getElementById('accessKey').focus();
            updateStatusBarColor(); // 更新状态栏颜色
        }

        // 隐藏KEY验证弹窗
        function hideKeyVerification() {
            document.getElementById('keyVerificationModal').style.display = 'none';
            document.getElementById('accessKey').value = '';
            document.getElementById('keyErrorMessage').style.display = 'none';
            updateStatusBarColor(); // 更新状态栏颜色
        }

        // 验证KEY
        function verifyKey() {
            const key = document.getElementById('accessKey').value.trim();
            const errorDiv = document.getElementById('keyErrorMessage');
            
            if (!key) {
                showError(errorDiv, '请输入访问KEY');
                return;
            }

            // 设置加载状态
            const modal = document.querySelector('#keyVerificationModal .modal');
            modal.classList.add('loading');

            // 调用后端验证KEY
            fetch('/systemStarPro/isKey', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'key=' + encodeURIComponent(key)
            })
            .then(response => response.json())
            .then(data => {
                modal.classList.remove('loading');
                
                if (data.code === 1) {
                    // KEY验证成功
                    verifiedKey = key;
                    hideKeyVerification();
                    showFirstInstall();
                } else {
                    // KEY验证失败
                    showError(errorDiv, data.msg || '访问KEY验证失败，请检查KEY是否正确');
                }
            })
            .catch(error => {
                modal.classList.remove('loading');
                console.error('验证KEY时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示第一步安装弹窗
        function showFirstInstall() {
            document.getElementById('firstInstallModal').style.display = 'flex';
        }

        // 隐藏第一步安装弹窗
        function hideFirstInstall() {
            document.getElementById('firstInstallModal').style.display = 'none';
            document.getElementById('installErrorMessage').style.display = 'none';
            document.getElementById('installSuccessMessage').style.display = 'none';
        }

        // 开始安装
        function startInstall() {
            const errorDiv = document.getElementById('installErrorMessage');
            const successDiv = document.getElementById('installSuccessMessage');
            const modal = document.querySelector('#firstInstallModal .modal');
            
            modal.classList.add('loading');
            hideMessage(errorDiv);
            hideMessage(successDiv);

            fetch('/installStar/newInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.text())
            .then(data => {
                modal.classList.remove('loading');
                
                if (data.includes('执行结束') || data.includes('安装执行完成')) {
                    showSuccess(successDiv, '第一步安装完成！');
                    setTimeout(() => {
                        hideFirstInstall();
                        showSecondInstall();
                    }, 1500);
                } else {
                    showError(errorDiv, data || '安装失败，请稍后重试');
                }
            })
            .catch(error => {
                modal.classList.remove('loading');
                console.error('安装时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 仅更新
        function updateOnly() {
            const errorDiv = document.getElementById('installErrorMessage');
            const successDiv = document.getElementById('installSuccessMessage');
            const modal = document.querySelector('#firstInstallModal .modal');
            
            modal.classList.add('loading');
            hideMessage(errorDiv);
            hideMessage(successDiv);

            fetch('/installStar/proInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.json())
            .then(data => {
                modal.classList.remove('loading');
                
                if (data.code === 1) {
                    showSuccess(successDiv, '更新完成！');
                    setTimeout(() => {
                        hideFirstInstall();
                    }, 1500);
                } else {
                    showError(errorDiv, data.msg || '更新失败，请稍后重试');
                }
            })
            .catch(error => {
                modal.classList.remove('loading');
                console.error('更新时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示第二步安装弹窗
        function showSecondInstall() {
            document.getElementById('secondInstallModal').style.display = 'flex';
        }

        // 隐藏第二步安装弹窗
        function hideSecondInstall() {
            document.getElementById('secondInstallModal').style.display = 'none';
            document.getElementById('secondInstallErrorMessage').style.display = 'none';
            document.getElementById('secondInstallSuccessMessage').style.display = 'none';
        }

        // 开始第二步安装
        function startSecondInstall() {
            const errorDiv = document.getElementById('secondInstallErrorMessage');
            const successDiv = document.getElementById('secondInstallSuccessMessage');
            const modal = document.querySelector('#secondInstallModal .modal');
            
            modal.classList.add('loading');
            hideMessage(errorDiv);
            hideMessage(successDiv);

            fetch('/installStar/proInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.json())
            .then(data => {
                modal.classList.remove('loading');
                
                if (data.code === 1) {
                    showSuccess(successDiv, '第二步安装完成！所有安装步骤已完成。');
                    setTimeout(() => {
                        hideSecondInstall();
                    }, 2000);
                } else {
                    showError(errorDiv, data.msg || '第二步安装失败，请稍后重试');
                }
            })
            .catch(error => {
                modal.classList.remove('loading');
                console.error('第二步安装时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示错误信息
        function showError(element, message) {
            element.textContent = message;
            element.style.display = 'block';
        }

        // 显示成功信息
        function showSuccess(element, message) {
            element.textContent = message;
            element.style.display = 'block';
        }

        // 隐藏信息
        function hideMessage(element) {
            element.style.display = 'none';
        }

        // 支持回车键提交
        document.getElementById('accessKey').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                verifyKey();
            }
        });

        // 点击遮罩关闭弹窗
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    overlay.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
