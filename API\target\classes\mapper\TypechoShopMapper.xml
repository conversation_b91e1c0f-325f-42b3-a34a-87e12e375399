<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoShopDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoShop" >
        <result column="id" property="id" />
        <result column="title" property="title" />
        <result column="imgurl" property="imgurl" />
        <result column="text" property="text" />
        <result column="price" property="price" />
        <result column="integral" property="integral" />
        <result column="num" property="num" />
        <result column="type" property="type" />
        <result column="value" property="value" />
        <result column="cid" property="cid" />
        <result column="uid" property="uid" />
        <result column="status" property="status" />
        <result column="vipDiscount" property="vipDiscount" />
        <result column="sellNum" property="sellNum" />
        <result column="isMd" property="isMd" />
        <result column="sort" property="sort" />
        <result column="subtype" property="subtype" />
        <result column="isView" property="isView" />

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `title`,
        `imgurl`,
        `text`,
        `price`,
        `integral`,
        `num`,
        `type`,
        `value`,
        `cid`,
        `uid`,
        `created`,
        `status`,
        `vipDiscount`,
        `sellNum`,
        `isMd`,
        `sort`,
        `subtype`,
        `isView`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoShop" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ${prefix}_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != title'>
                `title`,
            </if>
            <if test ='null != imgurl'>
                `imgurl`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != price'>
                `price`,
            </if>
            <if test ='null != integral'>
                `integral`,
            </if>
            <if test ='null != num'>
                `num`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != value'>
                `value`,
            </if>
            <if test ='null != cid'>
                `cid`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != status'>
                `status`,
            </if>
            <if test ='null != vipDiscount'>
                `vipDiscount`,
            </if>
            <if test ='null != isMd'>
                `isMd`,
            </if>
            <if test ='null != sort'>
                `sort`,
            </if>
            <if test ='null != subtype'>
                `subtype`,
            </if>
            <if test ='null != isView'>
                `isView`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != title'>
                #{title},
            </if>
            <if test ='null != imgurl'>
                #{imgurl},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != price'>
                #{price},
            </if>
            <if test ='null != integral'>
                #{integral},
            </if>
            <if test ='null != num'>
                #{num},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != value'>
                #{value},
            </if>
            <if test ='null != cid'>
                #{cid},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != status'>
                #{status},
            </if>
            <if test ='null != vipDiscount'>
                #{vipDiscount},
            </if>
            <if test ='null != isMd'>
                #{isMd},
            </if>
            <if test ='null != sort'>
                #{sort},
            </if>
            <if test ='null != subtype'>
                #{subtype},
            </if>
            <if test ='null != isView'>
                #{isView}
            </if>

        </trim>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoShop">
        UPDATE ${prefix}_shop
        <set>
            <if test ='null != title'>`title` = #{title},</if>
            <if test ='null != imgurl'>`imgurl` = #{imgurl},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != price'>`price` = #{price},</if>
            <if test ='null != integral'>`integral` = #{integral},</if>
            <if test ='null != num'>`num` = #{num},</if>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != value'>`value` = #{value},</if>
            <if test ='null != cid'>`cid` = #{cid},</if>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != status'>`status` = #{status},</if>
            <if test ='null != vipDiscount'>`vipDiscount` = #{vipDiscount},</if>
            <if test ='null != sellNum'>`sellNum` = #{sellNum},</if>
            <if test ='null != isMd'>`isMd` = #{isMd},</if>
            <if test ='null != sort'>`sort` = #{sort},</if>
            <if test ='null != subtype'>`subtype` = #{subtype},</if>
            <if test ='null != isView'>`isView` = #{isView}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_shop
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_shop WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shop
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shop
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != title'>
                and `title` = #{title}
            </if>
            <if test ='null != imgurl'>
                and `imgurl` = #{imgurl}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != price'>
                and `price` = #{price}
            </if>
            <if test ='null != num'>
                and `num` = #{num}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
            <if test ='null != value'>
                and `value` = #{value}
            </if>
            <if test ='null != cid'>
                and `cid` = #{cid}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != status'>
                and `status` = #{status}
            </if>
            <if test ='null != isMd'>
                and `isMd` = #{isMd}
            </if>
            <if test ='null != sort'>
                and `sort` = #{sort}
            </if>
            <if test ='null != subtype'>
                and `subtype` = #{subtype}
            </if>
            <if test ='null != isView'>
                and `isView` = #{isView}
            </if>
        </where>
        order by created desc
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shop
        <where>
            <if test ='null != typechoShop.id'>
                and `id` = #{typechoShop.id}
            </if>
            <if test ='null != typechoShop.title'>
                and `title` = #{typechoShop.title}
            </if>
            <if test ='null != typechoShop.imgurl'>
                and `imgurl` = #{typechoShop.imgurl}
            </if>
            <if test ='null != typechoShop.text'>
                and `text` = #{typechoShop.text}
            </if>
            <if test ='null != typechoShop.price'>
                and `price` = #{typechoShop.price}
            </if>
            <if test ='null != typechoShop.num'>
                and `num` = #{typechoShop.num}
            </if>
            <if test ='null != typechoShop.type'>
                and `type` = #{typechoShop.type}
            </if>
            <if test ='null != typechoShop.value'>
                and `value` = #{typechoShop.value}
            </if>
            <if test ='null != typechoShop.cid'>
                and `cid` = #{typechoShop.cid}
            </if>
            <if test ='null != typechoShop.uid'>
                and `uid` = #{typechoShop.uid}
            </if>
            <if test ='null != typechoShop.status'>
                and `status` = #{typechoShop.status}
            </if>
            <if test ='null != typechoShop.isMd'>
                and `isMd` = #{typechoShop.isMd}
            </if>
            <if test ='null != typechoShop.sort'>
                and `sort` = #{typechoShop.sort}
            </if>
            <if test ='null != typechoShop.subtype'>
                and `subtype` = #{typechoShop.subtype}
            </if>
            <if test ='null != typechoShop.isView'>
                and `isView` = #{typechoShop.isView}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`title`, ''), IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_shop
        <where>
            <if test ='null != typechoShop.id'>
                and `id` = #{typechoShop.id}
            </if>
            <if test ='null != typechoShop.title'>
                and `title` = #{typechoShop.title}
            </if>
            <if test ='null != typechoShop.imgurl'>
                and `imgurl` = #{typechoShop.imgurl}
            </if>
            <if test ='null != typechoShop.text'>
                and `text` = #{typechoShop.text}
            </if>
            <if test ='null != typechoShop.price'>
                and `price` = #{typechoShop.price}
            </if>
            <if test ='null != typechoShop.num'>
                and `num` = #{typechoShop.num}
            </if>
            <if test ='null != typechoShop.type'>
                and `type` = #{typechoShop.type}
            </if>
            <if test ='null != typechoShop.value'>
                and `value` = #{typechoShop.value}
            </if>
            <if test ='null != typechoShop.cid'>
                and `cid` = #{typechoShop.cid}
            </if>
            <if test ='null != typechoShop.uid'>
                and `uid` = #{typechoShop.uid}
            </if>
            <if test ='null != typechoShop.status'>
                and `status` = #{typechoShop.status}
            </if>
            <if test ='null != typechoShop.sort'>
                and `sort` = #{typechoShop.sort}
            </if>
            <if test ='null != typechoShop.subtype'>
                and `subtype` = #{typechoShop.subtype}
            </if>
            <if test ='null != typechoShop.status'>
                and `isView` = #{typechoShop.isView}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`title`, ''), IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
    </select>
</mapper>