<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoGptDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoGpt" >
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="source" property="source" />
        <result column="isVip" property="isVip" />
        <result column="price" property="price" />
        <result column="avatar" property="avatar" />
        <result column="intro" property="intro" />
        <result column="created" property="created" />
        <result column="appId" property="appId" />
        <result column="apiKey" property="apiKey" />
        <result column="type" property="type" />
        <result column="prompt" property="prompt" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `name`,
        `source`,
        `isVip`,
        `price`,
        `avatar`,
        `intro`,
        `created`,
        `appId`,
        `apiKey`,
        `type`,
        `prompt`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoGpt">
        INSERT INTO ${prefix}_gpt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != name'>
                `name`,
            </if>
            <if test ='null != source'>
                `source`,
            </if>
            <if test ='null != isVip'>
                `isVip`,
            </if>
            <if test ='null != price'>
                `price`,
            </if>
            <if test ='null != avatar'>
                `avatar`,
            </if>
            <if test ='null != intro'>
                `intro`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != appId'>
                `appId`,
            </if>
            <if test ='null != apiKey'>
                `apiKey`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != prompt'>
                `prompt`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != name'>
                #{name},
            </if>
            <if test ='null != source'>
                #{source},
            </if>
            <if test ='null != isVip'>
                #{isVip},
            </if>
            <if test ='null != price'>
                #{price},
            </if>
            <if test ='null != avatar'>
                #{avatar},
            </if>
            <if test ='null != intro'>
                #{intro},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != appId'>
                #{appId},
            </if>
            <if test ='null != apiKey'>
                #{apiKey},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != prompt'>
                #{prompt}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_gpt ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.name},
                #{curr.source},
                #{curr.isVip},
                #{curr.price},
                #{curr.avatar},
                #{curr.intro},
                #{curr.created}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoGpt">
        UPDATE ${prefix}_gpt
        <set>
            <if test ='null != name'>`name` = #{name},</if>
            <if test ='null != source'>`source` = #{source},</if>
            <if test ='null != isVip'>`isVip` = #{isVip},</if>
            <if test ='null != price'>`price` = #{price},</if>
            <if test ='null != avatar'>`avatar` = #{avatar},</if>
            <if test ='null != intro'>`intro` = #{intro},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != appId'>`appId` = #{appId},</if>
            <if test ='null != apiKey'>`apiKey` = #{apiKey},</if>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != prompt'>`prompt` = #{prompt}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_gpt
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_gpt WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != source'>
                and `source` = #{source}
            </if>
            <if test ='null != isVip'>
                and `isVip` = #{isVip}
            </if>
            <if test ='null != price'>
                and `price` = #{price}
            </if>
            <if test ='null != avatar'>
                and `avatar` = #{avatar}
            </if>
            <if test ='null != intro'>
                and `intro` = #{intro}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_gpt
        <where>
            <if test ='null != typechoGpt.id'>
                and `id` = #{typechoGpt.id}
            </if>
            <if test ='null != typechoGpt.name'>
                and `name` = #{typechoGpt.name}
            </if>
            <if test ='null != typechoGpt.source'>
                and `source` = #{typechoGpt.source}
            </if>
            <if test ='null != typechoGpt.isVip'>
                and `isVip` = #{typechoGpt.isVip}
            </if>
            <if test ='null != typechoGpt.price'>
                and `price` = #{typechoGpt.price}
            </if>
            <if test ='null != typechoGpt.avatar'>
                and `avatar` = #{typechoGpt.avatar}
            </if>
            <if test ='null != typechoGpt.intro'>
                and `intro` = #{typechoGpt.intro}
            </if>
            <if test ='null != typechoGpt.created'>
                and `created` = #{typechoGpt.created}
            </if>
            <if test ='null != typechoGpt.type'>
                and `type` = #{typechoGpt.type}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`name`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_gpt
        <where>
            <if test ='null != typechoGpt.id'>
                and `id` = #{typechoGpt.id}
            </if>
            <if test ='null != typechoGpt.name'>
                and `name` = #{typechoGpt.name}
            </if>
            <if test ='null != typechoGpt.source'>
                and `source` = #{typechoGpt.source}
            </if>
            <if test ='null != typechoGpt.isVip'>
                and `isVip` = #{typechoGpt.isVip}
            </if>
            <if test ='null != typechoGpt.price'>
                and `price` = #{typechoGpt.price}
            </if>
            <if test ='null != typechoGpt.avatar'>
                and `avatar` = #{typechoGpt.avatar}
            </if>
            <if test ='null != typechoGpt.intro'>
                and `intro` = #{typechoGpt.intro}
            </if>
            <if test ='null != typechoGpt.created'>
                and `created` = #{typechoGpt.created}
            </if>
            <if test ='null != typechoGpt.type'>
                and `type` = #{typechoGpt.type}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`name`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
    </select>
</mapper>