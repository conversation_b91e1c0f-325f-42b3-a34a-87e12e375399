<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoShoptypeDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoShoptype" >
        <result column="id" property="id" />
        <result column="parent" property="parent" />
        <result column="name" property="name" />
        <result column="pic" property="pic" />
        <result column="intro" property="intro" />
        <result column="orderKey" property="orderKey" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `parent`,
        `name`,
        `pic`,
        `intro`,
        `orderKey`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoShoptype">
        INSERT INTO ${prefix}_shoptype
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != parent'>
                `parent`,
            </if>
            <if test ='null != name'>
                `name`,
            </if>
            <if test ='null != pic'>
                `pic`,
            </if>
            <if test ='null != intro'>
                `intro`,
            </if>
            <if test ='null != orderKey'>
                `orderKey`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != parent'>
                #{parent},
            </if>
            <if test ='null != name'>
                #{name},
            </if>
            <if test ='null != pic'>
                #{pic},
            </if>
            <if test ='null != intro'>
                #{intro},
            </if>
            <if test ='null != orderKey'>
                #{orderKey}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_shoptype ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.parent},
                #{curr.name},
                #{curr.pic},
                #{curr.intro},
                #{curr.orderKey}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoShoptype">
        UPDATE ${prefix}_shoptype
        <set>
            <if test ='null != parent'>`parent` = #{parent},</if>
            <if test ='null != name'>`name` = #{name},</if>
            <if test ='null != pic'>`pic` = #{pic},</if>
            <if test ='null != intro'>`intro` = #{intro},</if>
            <if test ='null != orderKey'>`orderKey` = #{orderKey}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_shoptype
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_shoptype WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shoptype
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shoptype
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != parent'>
                and `parent` = #{parent}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != pic'>
                and `pic` = #{pic}
            </if>
            <if test ='null != intro'>
                and `intro` = #{intro}
            </if>
            <if test ='null != orderKey'>
                and `orderKey` = #{orderKey}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_shoptype
        <where>
            <if test ='null != typechoShoptype.id'>
                and `id` = #{typechoShoptype.id}
            </if>
            <if test ='null != typechoShoptype.parent'>
                and `parent` = #{typechoShoptype.parent}
            </if>
            <if test ='null != typechoShoptype.name'>
                and `name` = #{typechoShoptype.name}
            </if>
            <if test ='null != typechoShoptype.pic'>
                and `pic` = #{typechoShoptype.pic}
            </if>
            <if test ='null != typechoShoptype.intro'>
                and `intro` = #{typechoShoptype.intro}
            </if>
            <if test ='null != typechoShoptype.orderKey'>
                and `orderKey` = #{typechoShoptype.orderKey}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`name`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_shoptype
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != parent'>
                and `parent` = #{parent}
            </if>
            <if test ='null != name'>
                and `name` = #{name}
            </if>
            <if test ='null != pic'>
                and `pic` = #{pic}
            </if>
            <if test ='null != intro'>
                and `intro` = #{intro}
            </if>
            <if test ='null != orderKey'>
                and `orderKey` = #{orderKey}
            </if>
        </where>
    </select>
</mapper>