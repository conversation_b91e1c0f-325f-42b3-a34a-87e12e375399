(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{1:function(t,e,n){"use strict";n.d(e,"k",(function(){return x})),n.d(e,"m",(function(){return w})),n.d(e,"l",(function(){return _})),n.d(e,"e",(function(){return O})),n.d(e,"b",(function(){return $})),n.d(e,"s",(function(){return C})),n.d(e,"g",(function(){return j})),n.d(e,"h",(function(){return k})),n.d(e,"d",(function(){return S})),n.d(e,"r",(function(){return P})),n.d(e,"j",(function(){return E})),n.d(e,"t",(function(){return T})),n.d(e,"o",(function(){return I})),n.d(e,"q",(function(){return D})),n.d(e,"f",(function(){return L})),n.d(e,"c",(function(){return N})),n.d(e,"i",(function(){return U})),n.d(e,"p",(function(){return M})),n.d(e,"a",(function(){return Q})),n.d(e,"v",(function(){return J})),n.d(e,"n",(function(){return W})),n.d(e,"u",(function(){return X}));n(63),n(40),n(66),n(67),n(87),n(41),n(88);var r=n(15),o=n(12),c=n(28),f=n(24),l=(n(86),n(18),n(47),n(319),n(35),n(116),n(69),n(48),n(49),n(50),n(55),n(31),n(117),n(230),n(231),n(323),n(89),n(233),n(326),n(114),n(115),n(0)),h=n(33);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function m(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(c.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}function v(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,f=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){f=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(f)throw o}}}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function x(t){l.default.config.errorHandler&&l.default.config.errorHandler(t)}function w(t){return t.then((function(t){return t.default||t}))}function _(t){return t.$options&&"function"==typeof t.$options.fetch&&!t.$options.fetch.length}function O(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=t.$children||[],o=v(r);try{for(o.s();!(e=o.n()).done;){var c=e.value;c.$fetch?n.push(c):c.$children&&O(c,n)}}catch(t){o.e(t)}finally{o.f()}return n}function $(t,e){if(e||!t.options.__hasNuxtData){var n=t.options._originDataFn||t.options.data||function(){return{}};t.options._originDataFn=n,t.options.data=function(){var data=n.call(this,this);return this.$ssrContext&&(e=this.$ssrContext.asyncData[t.cid]),m(m({},data),e)},t.options.__hasNuxtData=!0,t._Ctor&&t._Ctor.options&&(t._Ctor.options.data=t.options.data)}}function C(t){return t.options&&t._Ctor===t||(t.options?(t._Ctor=t,t.extendOptions=t.options):(t=l.default.extend(t))._Ctor=t,!t.options.name&&t.options.__file&&(t.options.name=t.options.__file)),t}function j(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"components";return Array.prototype.concat.apply([],t.matched.map((function(t,r){return Object.keys(t[n]).map((function(o){return e&&e.push(r),t[n][o]}))})))}function k(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return j(t,e,"instances")}function S(t,e){return Array.prototype.concat.apply([],t.matched.map((function(t,n){return Object.keys(t.components).reduce((function(r,o){return t.components[o]?r.push(e(t.components[o],t.instances[o],t,o,n)):delete t.components[o],r}),[])})))}function P(t,e){return Promise.all(S(t,function(){var t=Object(o.a)(regeneratorRuntime.mark((function t(n,r,o,c){var f,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"!=typeof n||n.options){t.next=11;break}return t.prev=1,t.next=4,n();case 4:n=t.sent,t.next=11;break;case 7:throw t.prev=7,t.t0=t.catch(1),t.t0&&"ChunkLoadError"===t.t0.name&&"undefined"!=typeof window&&window.sessionStorage&&(f=Date.now(),(!(l=parseInt(window.sessionStorage.getItem("nuxt-reload")))||l+6e4<f)&&(window.sessionStorage.setItem("nuxt-reload",f),window.location.reload(!0))),t.t0;case 11:return o.components[c]=n=C(n),t.abrupt("return","function"==typeof e?e(n,r,o,c):n);case 13:case"end":return t.stop()}}),t,null,[[1,7]])})));return function(e,n,r,o){return t.apply(this,arguments)}}()))}function E(t){return R.apply(this,arguments)}function R(){return(R=Object(o.a)(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,P(e);case 4:return t.abrupt("return",m(m({},e),{},{meta:j(e).map((function(t,n){return m(m({},t.options.meta),(e.matched[n]||{}).meta)}))}));case 5:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function T(t,e){return A.apply(this,arguments)}function A(){return(A=Object(o.a)(regeneratorRuntime.mark((function t(e,n){var o,c,l,d;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.context||(e.context={isStatic:!0,isDev:!1,isHMR:!1,app:e,payload:n.payload,error:n.error,base:e.router.options.base,env:{baseUrl:"/"}},n.req&&(e.context.req=n.req),n.res&&(e.context.res=n.res),n.ssrContext&&(e.context.ssrContext=n.ssrContext),e.context.redirect=function(t,path,n){if(t){e.context._redirected=!0;var o=Object(r.a)(path);if("number"==typeof t||"undefined"!==o&&"object"!==o||(n=path||{},path=t,o=Object(r.a)(path),t=302),"object"===o&&(path=e.router.resolve(path).route.fullPath),!/(^[.]{1,2}\/)|(^\/(?!\/))/.test(path))throw path=Object(h.d)(path,n),window.location.replace(path),new Error("ERR_REDIRECT");e.context.next({path:path,query:n,status:t})}},e.context.nuxtState=window.__NUXT__),t.next=3,Promise.all([E(n.route),E(n.from)]);case 3:o=t.sent,c=Object(f.a)(o,2),l=c[0],d=c[1],n.route&&(e.context.route=l),n.from&&(e.context.from=d),e.context.next=n.next,e.context._redirected=!1,e.context._errored=!1,e.context.isHMR=!1,e.context.params=e.context.route.params||{},e.context.query=e.context.route.query||{};case 15:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function I(t,e){return!t.length||e._redirected||e._errored?Promise.resolve():D(t[0],e).then((function(){return I(t.slice(1),e)}))}function D(t,e){var n;return(n=2===t.length?new Promise((function(n){t(e,(function(t,data){t&&e.error(t),n(data=data||{})}))})):t(e))&&n instanceof Promise&&"function"==typeof n.then?n:Promise.resolve(n)}function L(base,t){if("hash"===t)return window.location.hash.replace(/^#\//,"");base=decodeURI(base).slice(0,-1);var path=decodeURI(window.location.pathname);base&&path.startsWith(base)&&(path=path.slice(base.length));var e=(path||"/")+window.location.search+window.location.hash;return Object(h.c)(e)}function N(t,e){return function(t,e){for(var n=new Array(t.length),i=0;i<t.length;i++)"object"===Object(r.a)(t[i])&&(n[i]=new RegExp("^(?:"+t[i].pattern+")$",H(e)));return function(e,r){for(var path="",data=e||{},o=(r||{}).pretty?B:encodeURIComponent,c=0;c<t.length;c++){var f=t[c];if("string"!=typeof f){var l=data[f.name||"pathMatch"],h=void 0;if(null==l){if(f.optional){f.partial&&(path+=f.prefix);continue}throw new TypeError('Expected "'+f.name+'" to be defined')}if(Array.isArray(l)){if(!f.repeat)throw new TypeError('Expected "'+f.name+'" to not repeat, but received `'+JSON.stringify(l)+"`");if(0===l.length){if(f.optional)continue;throw new TypeError('Expected "'+f.name+'" to not be empty')}for(var d=0;d<l.length;d++){if(h=o(l[d]),!n[c].test(h))throw new TypeError('Expected all "'+f.name+'" to match "'+f.pattern+'", but received `'+JSON.stringify(h)+"`");path+=(0===d?f.prefix:f.delimiter)+h}}else{if(h=f.asterisk?F(l):o(l),!n[c].test(h))throw new TypeError('Expected "'+f.name+'" to match "'+f.pattern+'", but received "'+h+'"');path+=f.prefix+h}}else path+=f}return path}}(function(t,e){var n,r=[],o=0,c=0,path="",f=e&&e.delimiter||"/";for(;null!=(n=K.exec(t));){var l=n[0],h=n[1],d=n.index;if(path+=t.slice(c,d),c=d+l.length,h)path+=h[1];else{var m=t[c],v=n[2],y=n[3],x=n[4],w=n[5],_=n[6],O=n[7];path&&(r.push(path),path="");var $=null!=v&&null!=m&&m!==v,C="+"===_||"*"===_,j="?"===_||"*"===_,k=n[2]||f,pattern=x||w;r.push({name:y||o++,prefix:v||"",delimiter:k,optional:j,repeat:C,partial:$,asterisk:Boolean(O),pattern:pattern?z(pattern):O?".*":"[^"+V(k)+"]+?"})}}c<t.length&&(path+=t.substr(c));path&&r.push(path);return r}(t,e),e)}function U(t,e){var n={},r=m(m({},t),e);for(var o in r)String(t[o])!==String(e[o])&&(n[o]=!0);return n}function M(t){var e;if(t.message||"string"==typeof t)e=t.message||t;else try{e=JSON.stringify(t,null,2)}catch(n){e="[".concat(t.constructor.name,"]")}return m(m({},t),{},{message:e,statusCode:t.statusCode||t.status||t.response&&t.response.status||500})}window.onNuxtReadyCbs=[],window.onNuxtReady=function(t){window.onNuxtReadyCbs.push(t)};var K=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function B(t,e){var n=e?/[?#]/g:/[/?#]/g;return encodeURI(t).replace(n,(function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}))}function F(t){return B(t,!0)}function V(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function z(t){return t.replace(/([=!:$/()])/g,"\\$1")}function H(t){return t&&t.sensitive?"":"i"}function Q(t,e,n){t.$options[e]||(t.$options[e]=[]),t.$options[e].includes(n)||t.$options[e].push(n)}var J=h.b,W=(h.e,h.a);function X(t){try{window.history.scrollRestoration=t}catch(t){}}},128:function(t,e,n){"use strict";var r=n(129),o=n.n(r);e.default=o.a},129:function(t,e){},134:function(t,e,n){n(48),n(114),n(115)},135:function(t,e,n){"use strict";n(75),n(18),n(41),n(114),n(115),n(69),n(47),n(55),n(35),n(63),n(49),n(31),n(40),n(66),n(67),n(50);var r=n(0);function o(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,f=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return f=t.done,t},e:function(t){l=!0,o=t},f:function(){try{f||null==n.return||n.return()}finally{if(l)throw o}}}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var f=window.requestIdleCallback||function(t){var e=Date.now();return setTimeout((function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})}),1)},l=window.cancelIdleCallback||function(t){clearTimeout(t)},h=window.IntersectionObserver&&new window.IntersectionObserver((function(t){t.forEach((function(t){var e=t.intersectionRatio,link=t.target;e<=0||!link.__prefetch||link.__prefetch()}))}));e.a={name:"NuxtLink",extends:r.default.component("RouterLink"),props:{prefetch:{type:Boolean,default:!0},noPrefetch:{type:Boolean,default:!1}},mounted:function(){this.prefetch&&!this.noPrefetch&&(this.handleId=f(this.observe,{timeout:2e3}))},beforeDestroy:function(){l(this.handleId),this.__observed&&(h.unobserve(this.$el),delete this.$el.__prefetch)},methods:{observe:function(){h&&this.shouldPrefetch()&&(this.$el.__prefetch=this.prefetchLink.bind(this),h.observe(this.$el),this.__observed=!0)},shouldPrefetch:function(){return this.getPrefetchComponents().length>0},canPrefetch:function(){var t=navigator.connection;return!(this.$nuxt.isOffline||t&&((t.effectiveType||"").includes("2g")||t.saveData))},getPrefetchComponents:function(){return this.$router.resolve(this.to,this.$route,this.append).resolved.matched.map((function(t){return t.components.default})).filter((function(t){return"function"==typeof t&&!t.options&&!t.__prefetched}))},prefetchLink:function(){if(this.canPrefetch()){h.unobserve(this.$el);var t,e=o(this.getPrefetchComponents());try{for(e.s();!(t=e.n()).done;){var n=t.value,r=n();r instanceof Promise&&r.catch((function(){})),n.__prefetched=!0}}catch(t){e.e(t)}finally{e.f()}}}}}},181:function(t,e,n){"use strict";e.a={}},185:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o}));var r=function(){var t=this._self._c;this._self._setupProxy;return t("Nuxt")},o=[]},235:function(t,e,n){},236:function(t,e,n){},270:function(t,e,n){"use strict";var r=n(185),o=n(128),c=n(29),component=Object(c.a)(o.default,r.a,r.b,!1,null,null,null);e.default=component.exports},274:function(t,e){t.exports={isPro:function(){return 1},isInstall:function(){return"/installStar/isInstall"},typechoInstall:function(){return"/installStar/typechoInstall"},toUtf8mb4:function(){return"/installStar/toUtf8mb4"},apiNewVersion:function(){return"/systemStarPro/apiNewVersion"},newInstall:function(){return"/installStar/newInstall"},proInstall:function(){return"/installStar/proInstall"},isKey:function(){return"/systemStarPro/isKey"},getConfig:function(){return"/systemStarPro/getConfig"},getApiConfig:function(){return"/systemStarPro/getApiConfig"},apiConfigUpdate:function(){return"/systemStarPro/apiConfigUpdate"},setupMysql:function(){return"/systemStarPro/setupMysql"},setupRedis:function(){return"/systemStarPro/setupRedis"},setupEmail:function(){return"/systemStarPro/setupEmail"},setupWebKey:function(){return"/systemStarPro/setupWebKey"},setupConfig:function(){return"/systemStarPro/setupConfig"},allConfig:function(){return"/systemStarPro/allConfig"},addVipType:function(){return"/systemStarPro/addVipType"},updateVipType:function(){return"/systemStarPro/updateVipType"},deleteVipType:function(){return"/systemStarPro/deleteVipType"},vipTypeList:function(){return"/systemStarPro/vipTypeList"},addApp:function(){return"/systemStarPro/addApp"},updateApp:function(){return"/systemStarPro/updateApp"},deleteApp:function(){return"/systemStarPro/deleteApp"},appList:function(){return"/systemStarPro/appList"},getEmailTemplateConfig:function(){return"/systemStarPro/getEmailTemplateConfig"},emailTemplateConfigUpdate:function(){return"/systemStarPro/emailTemplateConfigUpdate"},getRuleNews:function(){return"https://api.starpro.site/SProMetas/selectContents"},docUrl:function(){return"https://www.yuque.com/senyun-ev0j3/starpro"},vIOser:function(){return"/u680"},vl0ser:function(){return"/u825"},vlOser:function(){return"/ugit"},v10ser:function(){return"/u158"},v1oser:function(){return"/u256"},v1Oser:function(){return"/uck"},vI0ser:function(){return"/u355"},vIoser:function(){return"/u343"}}},276:function(t,e,n){"use strict";var r=n(12),o=(n(86),n(18),n(75),n(0)),c=n(1),f=window.__NUXT__;function l(){if(!this._hydrated)return this.$fetch()}function h(){if((t=this).$vnode&&t.$vnode.elm&&t.$vnode.elm.dataset&&t.$vnode.elm.dataset.fetchKey){var t;this._hydrated=!0,this._fetchKey=this.$vnode.elm.dataset.fetchKey;var data=f.fetch[this._fetchKey];if(data&&data._error)this.$fetchState.error=data._error;else for(var e in data)o.default.set(this.$data,e,data[e])}}function d(){var t=this;return this._fetchPromise||(this._fetchPromise=m.call(this).then((function(){delete t._fetchPromise}))),this._fetchPromise}function m(){return v.apply(this,arguments)}function v(){return(v=Object(r.a)(regeneratorRuntime.mark((function t(){var e,n,r,o=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.$nuxt.nbFetching++,this.$fetchState.pending=!0,this.$fetchState.error=null,this._hydrated=!1,e=null,n=Date.now(),t.prev=6,t.next=9,this.$options.fetch.call(this);case 9:t.next=15;break;case 11:t.prev=11,t.t0=t.catch(6),e=Object(c.p)(t.t0);case 15:if(!((r=this._fetchDelay-(Date.now()-n))>0)){t.next=19;break}return t.next=19,new Promise((function(t){return setTimeout(t,r)}));case 19:this.$fetchState.error=e,this.$fetchState.pending=!1,this.$fetchState.timestamp=Date.now(),this.$nextTick((function(){return o.$nuxt.nbFetching--}));case 23:case"end":return t.stop()}}),t,this,[[6,11]])})))).apply(this,arguments)}e.a={beforeCreate:function(){Object(c.l)(this)&&(this._fetchDelay="number"==typeof this.$options.fetchDelay?this.$options.fetchDelay:200,o.default.util.defineReactive(this,"$fetchState",{pending:!1,error:null,timestamp:Date.now()}),this.$fetch=d.bind(this),Object(c.a)(this,"created",h),Object(c.a)(this,"beforeMount",l))}}},277:function(t,e,n){n(278),t.exports=n(279)},279:function(t,e,n){"use strict";n.r(e),function(t){n(55),n(63),n(40),n(66),n(67);var e=n(15),r=n(12),o=(n(145),n(298),n(312),n(314),n(86),n(35),n(18),n(41),n(47),n(48),n(114),n(115),n(116),n(69),n(31),n(49),n(50),n(75),n(0)),c=n(265),f=n(181),l=n(1),h=n(34),d=n(276),m=n(135);function v(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return y(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return y(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,f=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){f=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(f)throw o}}}}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}o.default.__nuxt__fetch__mixin__||(o.default.mixin(d.a),o.default.__nuxt__fetch__mixin__=!0),o.default.component(m.a.name,m.a),o.default.component("NLink",m.a),t.fetch||(t.fetch=c.a);var x,w,_=[],O=window.__NUXT__||{},$=O.config||{};$._app&&(n.p=Object(l.v)($._app.cdnURL,$._app.assetsPath)),Object.assign(o.default.config,{silent:!0,performance:!1});var C=o.default.config.errorHandler||console.error;function j(t,e,n){for(var r=function(component){var t=function(component,t){if(!component||!component.options||!component.options[t])return{};var option=component.options[t];if("function"==typeof option){for(var e=arguments.length,n=new Array(e>2?e-2:0),r=2;r<e;r++)n[r-2]=arguments[r];return option.apply(void 0,n)}return option}(component,"transition",e,n)||{};return"string"==typeof t?{name:t}:t},o=n?Object(l.g)(n):[],c=Math.max(t.length,o.length),f=[],h=function(i){var e=Object.assign({},r(t[i])),n=Object.assign({},r(o[i]));Object.keys(e).filter((function(t){return void 0!==e[t]&&!t.toLowerCase().includes("leave")})).forEach((function(t){n[t]=e[t]})),f.push(n)},i=0;i<c;i++)h(i);return f}function k(t,e,n){return S.apply(this,arguments)}function S(){return(S=Object(r.a)(regeneratorRuntime.mark((function t(e,n,r){var o,c,f,h,d=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._routeChanged=Boolean(x.nuxt.err)||n.name!==e.name,this._paramChanged=!this._routeChanged&&n.path!==e.path,this._queryChanged=!this._paramChanged&&n.fullPath!==e.fullPath,this._diffQuery=this._queryChanged?Object(l.i)(e.query,n.query):[],(this._routeChanged||this._paramChanged)&&this.$loading.start&&!this.$loading.manual&&this.$loading.start(),t.prev=5,!this._queryChanged){t.next=12;break}return t.next=9,Object(l.r)(e,(function(t,e){return{Component:t,instance:e}}));case 9:o=t.sent,o.some((function(t){var r=t.Component,o=t.instance,c=r.options.watchQuery;return!0===c||(Array.isArray(c)?c.some((function(t){return d._diffQuery[t]})):"function"==typeof c&&c.apply(o,[e.query,n.query]))}))&&this.$loading.start&&!this.$loading.manual&&this.$loading.start();case 12:r(),t.next=26;break;case 15:if(t.prev=15,t.t0=t.catch(5),c=t.t0||{},f=c.statusCode||c.status||c.response&&c.response.status||500,h=c.message||"",!/^Loading( CSS)? chunk (\d)+ failed\./.test(h)){t.next=23;break}return window.location.reload(!0),t.abrupt("return");case 23:this.error({statusCode:f,message:h}),this.$nuxt.$emit("routeChanged",e,n,c),r();case 26:case"end":return t.stop()}}),t,this,[[5,15]])})))).apply(this,arguments)}function P(t,e){return O.serverRendered&&e&&Object(l.b)(t,e),t._Ctor=t,t}function E(t){return Object(l.d)(t,function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(e,n,r,o,c){var f;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"!=typeof e||e.options){t.next=4;break}return t.next=3,e();case 3:e=t.sent;case 4:return f=P(Object(l.s)(e),O.data?O.data[c]:null),r.components[o]=f,t.abrupt("return",f);case 7:case"end":return t.stop()}}),t)})));return function(e,n,r,o,c){return t.apply(this,arguments)}}())}function R(t,e,n){var r=this,o=[],c=!1;if(void 0!==n&&(o=[],(n=Object(l.s)(n)).options.middleware&&(o=o.concat(n.options.middleware)),t.forEach((function(t){t.options.middleware&&(o=o.concat(t.options.middleware))}))),o=o.map((function(t){return"function"==typeof t?t:("function"!=typeof f.a[t]&&(c=!0,r.error({statusCode:500,message:"Unknown middleware "+t})),f.a[t])})),!c)return Object(l.o)(o,e)}function T(t,e,n){return A.apply(this,arguments)}function A(){return A=Object(r.a)(regeneratorRuntime.mark((function t(e,n,o){var c,f,d,m,y,w,O,$,C,k,S,P,E,T,A,I=this;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!1!==this._routeChanged||!1!==this._paramChanged||!1!==this._queryChanged){t.next=2;break}return t.abrupt("return",o());case 2:return!1,e===n?(_=[],!0):(c=[],_=Object(l.g)(n,c).map((function(t,i){return Object(l.c)(n.matched[c[i]].path)(n.params)}))),f=!1,d=function(path){n.path===path.path&&I.$loading.finish&&I.$loading.finish(),n.path!==path.path&&I.$loading.pause&&I.$loading.pause(),f||(f=!0,o(path))},t.next=8,Object(l.t)(x,{route:e,from:n,next:d.bind(this)});case 8:if(this._dateLastError=x.nuxt.dateErr,this._hadError=Boolean(x.nuxt.err),m=[],(y=Object(l.g)(e,m)).length){t.next=27;break}return t.next=15,R.call(this,y,x.context);case 15:if(!f){t.next=17;break}return t.abrupt("return");case 17:return w=(h.a.options||h.a).layout,t.next=20,this.loadLayout("function"==typeof w?w.call(h.a,x.context):w);case 20:return O=t.sent,t.next=23,R.call(this,y,x.context,O);case 23:if(!f){t.next=25;break}return t.abrupt("return");case 25:return x.context.error({statusCode:404,message:"This page could not be found"}),t.abrupt("return",o());case 27:return y.forEach((function(t){t._Ctor&&t._Ctor.options&&(t.options.asyncData=t._Ctor.options.asyncData,t.options.fetch=t._Ctor.options.fetch)})),this.setTransitions(j(y,e,n)),t.prev=29,t.next=32,R.call(this,y,x.context);case 32:if(!f){t.next=34;break}return t.abrupt("return");case 34:if(!x.context._errored){t.next=36;break}return t.abrupt("return",o());case 36:return"function"==typeof($=y[0].options.layout)&&($=$(x.context)),t.next=40,this.loadLayout($);case 40:return $=t.sent,t.next=43,R.call(this,y,x.context,$);case 43:if(!f){t.next=45;break}return t.abrupt("return");case 45:if(!x.context._errored){t.next=47;break}return t.abrupt("return",o());case 47:C=!0,t.prev=48,k=v(y),t.prev=50,k.s();case 52:if((S=k.n()).done){t.next=63;break}if("function"==typeof(P=S.value).options.validate){t.next=56;break}return t.abrupt("continue",61);case 56:return t.next=58,P.options.validate(x.context);case 58:if(C=t.sent){t.next=61;break}return t.abrupt("break",63);case 61:t.next=52;break;case 63:t.next=68;break;case 65:t.prev=65,t.t0=t.catch(50),k.e(t.t0);case 68:return t.prev=68,k.f(),t.finish(68);case 71:t.next=77;break;case 73:return t.prev=73,t.t1=t.catch(48),this.error({statusCode:t.t1.statusCode||"500",message:t.t1.message}),t.abrupt("return",o());case 77:if(C){t.next=80;break}return this.error({statusCode:404,message:"This page could not be found"}),t.abrupt("return",o());case 80:return t.next=82,Promise.all(y.map(function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(r,i){var o,c,f,h,d,v,y,w,p;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r._path=Object(l.c)(e.matched[m[i]].path)(e.params),r._dataRefresh=!1,o=r._path!==_[i],I._routeChanged&&o?r._dataRefresh=!0:I._paramChanged&&o?(c=r.options.watchParam,r._dataRefresh=!1!==c):I._queryChanged&&(!0===(f=r.options.watchQuery)?r._dataRefresh=!0:Array.isArray(f)?r._dataRefresh=f.some((function(t){return I._diffQuery[t]})):"function"==typeof f&&(E||(E=Object(l.h)(e)),r._dataRefresh=f.apply(E[i],[e.query,n.query]))),I._hadError||!I._isMounted||r._dataRefresh){t.next=6;break}return t.abrupt("return");case 6:return h=[],d=r.options.asyncData&&"function"==typeof r.options.asyncData,v=Boolean(r.options.fetch)&&r.options.fetch.length,y=d&&v?30:45,d&&((w=Object(l.q)(r.options.asyncData,x.context)).then((function(t){Object(l.b)(r,t),I.$loading.increase&&I.$loading.increase(y)})),h.push(w)),I.$loading.manual=!1===r.options.loading,v&&((p=r.options.fetch(x.context))&&(p instanceof Promise||"function"==typeof p.then)||(p=Promise.resolve(p)),p.then((function(t){I.$loading.increase&&I.$loading.increase(y)})),h.push(p)),t.abrupt("return",Promise.all(h));case 14:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()));case 82:f||(this.$loading.finish&&!this.$loading.manual&&this.$loading.finish(),o()),t.next=99;break;case 85:if(t.prev=85,t.t2=t.catch(29),"ERR_REDIRECT"!==(T=t.t2||{}).message){t.next=90;break}return t.abrupt("return",this.$nuxt.$emit("routeChanged",e,n,T));case 90:return _=[],Object(l.k)(T),"function"==typeof(A=(h.a.options||h.a).layout)&&(A=A(x.context)),t.next=96,this.loadLayout(A);case 96:this.error(T),this.$nuxt.$emit("routeChanged",e,n,T),o();case 99:case"end":return t.stop()}}),t,this,[[29,85],[48,73],[50,65,68,71]])}))),A.apply(this,arguments)}function I(t,n){Object(l.d)(t,(function(t,n,r,c){return"object"!==Object(e.a)(t)||t.options||((t=o.default.extend(t))._Ctor=t,r.components[c]=t),t}))}function D(t){var e=Boolean(this.$options.nuxt.err);this._hadError&&this._dateLastError===this.$options.nuxt.dateErr&&(e=!1);var n=e?(h.a.options||h.a).layout:t.matched[0].components.default.options.layout;"function"==typeof n&&(n=n(x.context)),this.setLayout(n)}function L(t){t._hadError&&t._dateLastError===t.$options.nuxt.dateErr&&t.error()}function N(t,e){var n=this;if(!1!==this._routeChanged||!1!==this._paramChanged||!1!==this._queryChanged){var r=Object(l.h)(t),c=Object(l.g)(t),f=!1;o.default.nextTick((function(){r.forEach((function(t,i){if(t&&!t._isDestroyed&&t.constructor._dataRefresh&&c[i]===t.constructor&&!0!==t.$vnode.data.keepAlive&&"function"==typeof t.constructor.options.data){var e=t.constructor.options.data.call(t);for(var n in e)o.default.set(t.$data,n,e[n]);f=!0}})),f&&window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")})),L(n)}))}}function U(t){window.onNuxtReadyCbs.forEach((function(e){"function"==typeof e&&e(t)})),"function"==typeof window._onNuxtLoaded&&window._onNuxtLoaded(t),w.afterEach((function(e,n){o.default.nextTick((function(){return t.$nuxt.$emit("routeChanged",e,n)}))}))}function M(){return(M=Object(r.a)(regeneratorRuntime.mark((function t(e){var n,r,c,f,h;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return x=e.app,w=e.router,n=new o.default(x),r=O.layout||"default",t.next=6,n.loadLayout(r);case 6:return n.setLayout(r),c=function(){n.$mount("#__nuxt"),w.afterEach(I),w.afterEach(D.bind(n)),w.afterEach(N.bind(n)),o.default.nextTick((function(){U(n)}))},t.next=10,Promise.all(E(x.context.route));case 10:if(f=t.sent,n.setTransitions=n.$options.nuxt.setTransitions.bind(n),f.length&&(n.setTransitions(j(f,w.currentRoute)),_=w.currentRoute.matched.map((function(t){return Object(l.c)(t.path)(w.currentRoute.params)}))),n.$loading={},O.error&&n.error(O.error),w.beforeEach(k.bind(n)),w.beforeEach(T.bind(n)),!O.serverRendered||!Object(l.n)(O.routePath,n.context.route.path)){t.next=19;break}return t.abrupt("return",c());case 19:return h=function(){I(w.currentRoute,w.currentRoute),D.call(n,w.currentRoute),L(n),c()},t.next=22,new Promise((function(t){return setTimeout(t,0)}));case 22:T.call(n,w.currentRoute,w.currentRoute,(function(path){if(path){var t=w.afterEach((function(e,n){t(),h()}));w.push(path,void 0,(function(t){t&&C(t)}))}else h()}));case 23:case"end":return t.stop()}}),t)})))).apply(this,arguments)}Object(h.b)(null,O.config).then((function(t){return M.apply(this,arguments)})).catch(C)}.call(this,n(30))},34:function(t,e,n){"use strict";n.d(e,"b",(function(){return wt})),n.d(e,"a",(function(){return R}));n(48),n(40),n(47),n(87),n(41),n(88);var r=n(12),o=n(28),c=(n(86),n(35),n(69),n(18),n(31),n(117),n(0)),f=n(268),l=n(182),h=n.n(l),d=n(74),m=n.n(d),v=(n(49),n(50),n(183)),y=n(33),x=n(1);"scrollRestoration"in window.history&&(Object(x.u)("manual"),window.addEventListener("beforeunload",(function(){Object(x.u)("auto")})),window.addEventListener("load",(function(){Object(x.u)("manual")})));function w(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function _(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?w(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):w(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var O=function(){};c.default.use(v.a);var $={mode:"history",base:"/",linkActiveClass:"nuxt-link-active",linkExactActiveClass:"nuxt-link-exact-active",scrollBehavior:function(t,e,n){var r=!1,o=t!==e;n?r=n:o&&function(t){var e=Object(x.g)(t);if(1===e.length){var n=e[0].options;return!1!==(void 0===n?{}:n).scrollToTop}return e.some((function(t){var e=t.options;return e&&e.scrollToTop}))}(t)&&(r={x:0,y:0});var c=window.$nuxt;return(!o||t.path===e.path&&t.hash!==e.hash)&&c.$nextTick((function(){return c.$emit("triggerScroll")})),new Promise((function(e){c.$once("triggerScroll",(function(){if(t.hash){var n=t.hash;void 0!==window.CSS&&void 0!==window.CSS.escape&&(n="#"+window.CSS.escape(n.substr(1)));try{document.querySelector(n)&&(r={selector:n})}catch(t){console.warn("Failed to save scroll position. Please add CSS.escape() polyfill (https://github.com/mathiasbynens/CSS.escape).")}}e(r)}))}))},routes:[{path:"/",component:function(){return Object(x.m)(n.e(2).then(n.bind(null,439)))},name:"index"}],fallback:!1};function C(t,e){var base=e._app&&e._app.basePath||$.base,n=new v.a(_(_({},$),{},{base:base})),r=n.push;n.push=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:O,n=arguments.length>2?arguments[2]:void 0;return r.call(this,t,e,n)};var o=n.resolve.bind(n);return n.resolve=function(t,e,n){return"string"==typeof t&&(t=Object(y.c)(t)),o(t,e,n)},n}var j={name:"NuxtChild",functional:!0,props:{nuxtChildKey:{type:String,default:""},keepAlive:Boolean,keepAliveProps:{type:Object,default:void 0}},render:function(t,e){var n=e.parent,data=e.data,r=e.props,o=n.$createElement;data.nuxtChild=!0;for(var c=n,f=n.$nuxt.nuxt.transitions,l=n.$nuxt.nuxt.defaultTransition,h=0;n;)n.$vnode&&n.$vnode.data.nuxtChild&&h++,n=n.$parent;data.nuxtChildDepth=h;var d=f[h]||l,m={};k.forEach((function(t){void 0!==d[t]&&(m[t]=d[t])}));var v={};S.forEach((function(t){"function"==typeof d[t]&&(v[t]=d[t].bind(c))}));var y=v.beforeEnter;if(v.beforeEnter=function(t){if(window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")})),y)return y.call(c,t)},!1===d.css){var x=v.leave;(!x||x.length<2)&&(v.leave=function(t,e){x&&x.call(c,t),c.$nextTick(e)})}var w=o("routerView",data);return r.keepAlive&&(w=o("keep-alive",{props:r.keepAliveProps},[w])),o("transition",{props:m,on:v},[w])}},k=["name","mode","appear","css","type","duration","enterClass","leaveClass","appearClass","enterActiveClass","enterActiveClass","leaveActiveClass","appearActiveClass","enterToClass","leaveToClass","appearToClass"],S=["beforeEnter","enter","afterEnter","enterCancelled","beforeLeave","leave","afterLeave","leaveCancelled","beforeAppear","appear","afterAppear","appearCancelled"],P={name:"NuxtError",props:{error:{type:Object,default:null}},computed:{statusCode:function(){return this.error&&this.error.statusCode||500},message:function(){return this.error.message||"Error"}},head:function(){return{title:this.message,meta:[{name:"viewport",content:"width=device-width,initial-scale=1.0,minimum-scale=1.0"}]}}},E=(n(340),n(29)),R=Object(E.a)(P,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"__nuxt-error-page"},[e("div",{staticClass:"error"},[e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"90",height:"90",fill:"#DBE1EC",viewBox:"0 0 48 48"}},[e("path",{attrs:{d:"M22 30h4v4h-4zm0-16h4v12h-4zm1.99-10C12.94 4 4 12.95 4 24s8.94 20 19.99 20S44 35.05 44 24 35.04 4 23.99 4zM24 40c-8.84 0-16-7.16-16-16S15.16 8 24 8s16 7.16 16 16-7.16 16-16 16z"}})]),t._v(" "),e("div",{staticClass:"title"},[t._v(t._s(t.message))]),t._v(" "),404===t.statusCode?e("p",{staticClass:"description"},[void 0===t.$route?e("a",{staticClass:"error-link",attrs:{href:"/"}}):e("NuxtLink",{staticClass:"error-link",attrs:{to:"/"}},[t._v("Back to the home page")])],1):t._e(),t._v(" "),t._m(0)])])}),[function(){var t=this._self._c;return t("div",{staticClass:"logo"},[t("a",{attrs:{href:"https://nuxtjs.org",target:"_blank",rel:"noopener"}},[this._v("Nuxt")])])}],!1,null,null,null).exports,T=n(24),A=(n(89),{name:"Nuxt",components:{NuxtChild:j,NuxtError:R},props:{nuxtChildKey:{type:String,default:void 0},keepAlive:Boolean,keepAliveProps:{type:Object,default:void 0},name:{type:String,default:"default"}},errorCaptured:function(t){this.displayingNuxtError&&(this.errorFromNuxtError=t,this.$forceUpdate())},computed:{routerViewKey:function(){if(void 0!==this.nuxtChildKey||this.$route.matched.length>1)return this.nuxtChildKey||Object(x.c)(this.$route.matched[0].path)(this.$route.params);var t=Object(T.a)(this.$route.matched,1)[0];if(!t)return this.$route.path;var e=t.components.default;if(e&&e.options){var n=e.options;if(n.key)return"function"==typeof n.key?n.key(this.$route):n.key}return/\/$/.test(t.path)?this.$route.path:this.$route.path.replace(/\/$/,"")}},beforeCreate:function(){c.default.util.defineReactive(this,"nuxt",this.$root.$options.nuxt)},render:function(t){var e=this;return this.nuxt.err?this.errorFromNuxtError?(this.$nextTick((function(){return e.errorFromNuxtError=!1})),t("div",{},[t("h2","An error occurred while showing the error page"),t("p","Unfortunately an error occurred and while showing the error page another error occurred"),t("p","Error details: ".concat(this.errorFromNuxtError.toString())),t("nuxt-link",{props:{to:"/"}},"Go back to home")])):(this.displayingNuxtError=!0,this.$nextTick((function(){return e.displayingNuxtError=!1})),t(R,{props:{error:this.nuxt.err}})):t("NuxtChild",{key:this.routerViewKey,props:this.$props})}}),I=(n(55),n(63),n(66),n(67),n(75),{name:"NuxtLoading",data:function(){return{percent:0,show:!1,canSucceed:!0,reversed:!1,skipTimerCount:0,rtl:!1,throttle:200,duration:5e3,continuous:!1}},computed:{left:function(){return!(!this.continuous&&!this.rtl)&&(this.rtl?this.reversed?"0px":"auto":this.reversed?"auto":"0px")}},beforeDestroy:function(){this.clear()},methods:{clear:function(){clearInterval(this._timer),clearTimeout(this._throttle),this._timer=null},start:function(){var t=this;return this.clear(),this.percent=0,this.reversed=!1,this.skipTimerCount=0,this.canSucceed=!0,this.throttle?this._throttle=setTimeout((function(){return t.startTimer()}),this.throttle):this.startTimer(),this},set:function(t){return this.show=!0,this.canSucceed=!0,this.percent=Math.min(100,Math.max(0,Math.floor(t))),this},get:function(){return this.percent},increase:function(t){return this.percent=Math.min(100,Math.floor(this.percent+t)),this},decrease:function(t){return this.percent=Math.max(0,Math.floor(this.percent-t)),this},pause:function(){return clearInterval(this._timer),this},resume:function(){return this.startTimer(),this},finish:function(){return this.percent=this.reversed?0:100,this.hide(),this},hide:function(){var t=this;return this.clear(),setTimeout((function(){t.show=!1,t.$nextTick((function(){t.percent=0,t.reversed=!1}))}),500),this},fail:function(t){return this.canSucceed=!1,this},startTimer:function(){var t=this;this.show||(this.show=!0),void 0===this._cut&&(this._cut=1e4/Math.floor(this.duration)),this._timer=setInterval((function(){t.skipTimerCount>0?t.skipTimerCount--:(t.reversed?t.decrease(t._cut):t.increase(t._cut),t.continuous&&(t.percent>=100||t.percent<=0)&&(t.skipTimerCount=1,t.reversed=!t.reversed))}),100)}},render:function(t){var e=t(!1);return this.show&&(e=t("div",{staticClass:"nuxt-progress",class:{"nuxt-progress-notransition":this.skipTimerCount>0,"nuxt-progress-failed":!this.canSucceed},style:{width:this.percent+"%",left:this.left}})),e}}),D=(n(341),Object(E.a)(I,undefined,undefined,!1,null,null,null).exports),L=(n(342),n(343),n(344),{data:function(){return{menuShow:!1,menu:[],key:"",IntervalKey:null,isPro:0}},beforeDestroy:function(){},created:function(){},mounted:function(){var t=this;t.isPro=t.$api.isPro(),localStorage.getItem("webkey")?t.key=localStorage.getItem("webkey"):(t.$message({message:"请先完成Key认证！",type:"error"}),t.$router.push({path:"/"})),t.isKey(),t.$once("hook:beforeDestroy",(function(){clearInterval(t.IntervalKey),t.IntervalKey=null})),clearInterval(t.IntervalKey),t.IntervalKey=setInterval((function(){t.isKey()}),2e4)},methods:{Exit:function(){localStorage.removeItem("webkey"),this.$message({message:"退出成功！",type:"success"}),this.$router.push({path:"/"})},handleOpen:function(t,e){console.log(t,e)},handleClose:function(t,e){console.log(t,e)},goPage:function(link){if(""==link)return!1;this.$router.push({path:link})},isKey:function(){var t=this,data=(t.key,{webkey:t.key});t.$axios.$post(t.$api.isKey(),this.qs.stringify(data)).then((function(e){1!=e.code&&(t.$message({message:e.msg,type:"error"}),t.$router.push({path:"/"}))})).catch((function(e){console.log(e),t.$message({message:"接口请求异常，请检查网络！",type:"error"})}))}}}),N=Object(E.a)(L,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"container"},[e("div",{staticClass:"header"},[e("div",{staticClass:"menuShow"},[e("a",{attrs:{href:"javascript:;"},on:{click:function(e){t.menuShow=!t.menuShow}}},[t.menuShow?e("i",{staticClass:"el-icon-s-fold"}):t._e(),t._v(" "),t.menuShow?t._e():e("i",{staticClass:"el-icon-s-unfold"})])]),t._v(" "),e("div",{staticClass:"logo"},[e("nuxt-link",{attrs:{to:"/"}},[e("span",{staticClass:"star-logo1 font40 left20"},[t._v("StarProApi")])])],1),t._v(" "),e("div",{staticClass:"header-operate"},[e("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.Exit()}}},[t._v("退出")])],1)]),t._v(" "),e("div",{staticClass:"main"},[e("div",{staticClass:"menu-bg",class:t.menuShow?"show":"",on:{click:function(e){t.menuShow=!1}}}),t._v(" "),e("div",{staticClass:"menu",class:t.menuShow?"show":""},[e("el-menu",{staticClass:"el-menu-vertical",attrs:{"default-active":"2"},on:{open:t.handleOpen,close:t.handleClose}},[t._l(t.menu,(function(n,r){return[n.subList.length>0?e("el-submenu",{attrs:{index:(r+1).toString()}},[e("template",{slot:"title"},[e("i",{class:n.icon}),t._v(" "),e("span",[t._v(t._s(n.name))])]),t._v(" "),e("el-menu-item-group",[t._l(n.subList,(function(sub,n){return sub.isPro<=t.isPro?[e("el-menu-item",{attrs:{index:(r+1).toString()+"-"+n},on:{click:function(e){return t.goPage(sub.link)}}},[t._v(t._s(sub.name))])]:t._e()}))],2)],2):e("el-menu-item",{attrs:{index:(r+1).toString()},on:{click:function(e){return t.goPage(n.link)}}},[e("i",{class:n.icon}),t._v(" "),e("span",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(n.name))])])]}))],2)],1),t._v(" "),e("div",{staticClass:"page-main"},[e("Nuxt"),t._v(" "),t._m(0)],1)])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"footer"},[e("div",{staticClass:"footer-links"},[e("a",{attrs:{href:"https://www.yuque.com/senyun-ev0j3/starpro",target:"_blank"}},[t._v("官方文档")]),t._v(" "),e("a",{attrs:{href:"http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=T1gn76eBWb8kHhZRHkpzrlxTbamBkmPf&authKey=RQ%2BY6O01X7khvbkBB%2FJzmEWXRYuAQYD46cMksQw9tRwmffOfzb0MicfUf4Voe16R&noverify=0&group_code=752454468",target:"_blank"}},[t._v("QQ交流群")]),t._v(" "),e("a",{attrs:{href:"https://www.starpro.site/zanzhu.html",target:"_blank"}},[t._v("赞助作者")])]),t._v(" "),e("div",{staticClass:"footer-copy"},[t._v("\n          © 2023 - 2024 StarPro\n        ")])])}],!1,null,null,null).exports,U=n(270),M=Object(E.a)({},(function(){return(0,this._self._c)("Nuxt")}),[],!1,null,null,null).exports;function K(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return B(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return B(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,f=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){f=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(f)throw o}}}}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var F={_layout:Object(x.s)(N),_no:Object(x.s)(U.default),_default:Object(x.s)(M)},V={render:function(t,e){var n=t("NuxtLoading",{ref:"loading"}),r=t(this.layout||"nuxt"),o=t("div",{domProps:{id:"__layout"},key:this.layoutName},[r]),c=t("transition",{props:{name:"layout",mode:"out-in"},on:{beforeEnter:function(t){window.$nuxt.$nextTick((function(){window.$nuxt.$emit("triggerScroll")}))}}},[o]);return t("div",{domProps:{id:"__nuxt"}},[n,c])},data:function(){return{isOnline:!0,layout:null,layoutName:"",nbFetching:0}},beforeCreate:function(){c.default.util.defineReactive(this,"nuxt",this.$options.nuxt)},created:function(){this.$root.$options.$nuxt=this,window.$nuxt=this,this.refreshOnlineStatus(),window.addEventListener("online",this.refreshOnlineStatus),window.addEventListener("offline",this.refreshOnlineStatus),this.error=this.nuxt.error,this.context=this.$options.context},mounted:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.$loading=t.$refs.loading;case 1:case"end":return e.stop()}}),e)})))()},watch:{"nuxt.err":"errorChanged"},computed:{isOffline:function(){return!this.isOnline},isFetching:function(){return this.nbFetching>0},isPreview:function(){return Boolean(this.$options.previewData)}},methods:{refreshOnlineStatus:function(){void 0===window.navigator.onLine?this.isOnline=!0:this.isOnline=window.navigator.onLine},refresh:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){var n,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((n=Object(x.h)(t.$route)).length){e.next=3;break}return e.abrupt("return");case 3:return t.$loading.start(),r=n.map((function(e){var p=[];if(e.$options.fetch&&e.$options.fetch.length&&p.push(Object(x.q)(e.$options.fetch,t.context)),e.$fetch)p.push(e.$fetch());else{var n,r=K(Object(x.e)(e.$vnode.componentInstance));try{for(r.s();!(n=r.n()).done;){var component=n.value;p.push(component.$fetch())}}catch(t){r.e(t)}finally{r.f()}}return e.$options.asyncData&&p.push(Object(x.q)(e.$options.asyncData,t.context).then((function(t){for(var n in t)c.default.set(e.$data,n,t[n])}))),Promise.all(p)})),e.prev=5,e.next=8,Promise.all(r);case 8:e.next=15;break;case 10:e.prev=10,e.t0=e.catch(5),t.$loading.fail(e.t0),Object(x.k)(e.t0),t.error(e.t0);case 15:t.$loading.finish();case 16:case"end":return e.stop()}}),e,null,[[5,10]])})))()},errorChanged:function(){if(this.nuxt.err){this.$loading&&(this.$loading.fail&&this.$loading.fail(this.nuxt.err),this.$loading.finish&&this.$loading.finish());var t=(R.options||R).layout;"function"==typeof t&&(t=t(this.context)),this.setLayout(t)}},setLayout:function(t){return t&&F["_"+t]||(t="default"),this.layoutName=t,this.layout=F["_"+t],this.layout},loadLayout:function(t){return t&&F["_"+t]||(t="default"),Promise.resolve(F["_"+t])}},components:{NuxtLoading:D}},z=n(134);for(var H in z)c.default.component(H,z[H]),c.default.component("Lazy"+H,z[H]);var Q=n(93),J=n.n(Q),W=n(271);function X(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function Y(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?X(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):X(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}function G(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=function(t,e){if(!t)return;if("string"==typeof t)return Z(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Z(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,f=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return c=t.done,t},e:function(t){f=!0,o=t},f:function(){try{c||null==n.return||n.return()}finally{if(f)throw o}}}}function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}for(var tt={setBaseURL:function(t){this.defaults.baseURL=t},setHeader:function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"common",o=G(Array.isArray(r)?r:[r]);try{for(o.s();!(n=o.n()).done;){var c=n.value;e?this.defaults.headers[c][t]=e:delete this.defaults.headers[c][t]}}catch(t){o.e(t)}finally{o.f()}},setToken:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"common",r=t?(e?e+" ":"")+t:null;this.setHeader("Authorization",r,n)},onRequest:function(t){this.interceptors.request.use((function(e){return t(e)||e}))},onResponse:function(t){this.interceptors.response.use((function(e){return t(e)||e}))},onRequestError:function(t){this.interceptors.request.use(void 0,(function(e){return t(e)||Promise.reject(e)}))},onResponseError:function(t){this.interceptors.response.use(void 0,(function(e){return t(e)||Promise.reject(e)}))},onError:function(t){this.onRequestError(t),this.onResponseError(t)},create:function(t){return it(Object(W.a)(t,this.defaults))}},et=function(){var t=ot[nt];tt["$"+t]=function(){return this[t].apply(this,arguments).then((function(t){return t&&t.data}))}},nt=0,ot=["request","delete","get","head","options","post","put","patch"];nt<ot.length;nt++)et();var it=function(t){var e=J.a.create(t);return e.CancelToken=J.a.CancelToken,e.isCancel=J.a.isCancel,function(t){for(var e in tt)t[e]=tt[e].bind(t)}(e),e.onRequest((function(t){t.headers=Y(Y({},e.defaults.headers.common),t.headers)})),at(e),e},at=function(t){var e={finish:function(){},start:function(){},fail:function(){},set:function(){}},n=function(){var t="undefined"!=typeof window&&window.$nuxt;return t&&t.$loading&&t.$loading.set?t.$loading:e},r=0;t.onRequest((function(t){t&&!1===t.progress||r++})),t.onResponse((function(t){t&&t.config&&!1===t.config.progress||--r<=0&&(r=0,n().finish())})),t.onError((function(t){t&&t.config&&!1===t.config.progress||(r--,J.a.isCancel(t)?r<=0&&(r=0,n().finish()):(n().fail(),n().finish()))}));var o=function(t){if(r&&t.total){var progress=100*t.loaded/(t.total*r);n().set(Math.min(100,progress))}};t.defaults.onUploadProgress=o,t.defaults.onDownloadProgress=o},st=function(t,e){var n=t.$config&&t.$config.axios||{},r=n.browserBaseURL||n.browserBaseUrl||n.baseURL||n.baseUrl||"http://localhost:3000/";var o=it({baseURL:r,headers:{common:{Accept:"application/json, text/plain, */*"},delete:{},get:{},head:{},post:{},put:{},patch:{}}});t.$axios=o,e("axios",o)},ut=n(272),ct=n.n(ut),ft=n(273),lt=n.n(ft);c.default.use(ct.a,{locale:lt.a});var pt=function(t){var e=t.$axios;t.redirect;e.defaults.timeout=6e4,e.defaults.baseURL="/",e.setHeader("Content-Type","application/x-www-form-urlencoded"),e.onRequest((function(t){})),e.onResponse((function(t){})),e.onError((function(t){}))},ht=n(274),mt=n.n(ht),vt=n(275),yt=n.n(vt);function bt(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function gt(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?bt(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):bt(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}c.default.prototype.qs=yt.a,c.default.prototype.$api=mt.a,c.default.component(h.a.name,h.a),c.default.component(m.a.name,gt(gt({},m.a),{},{render:function(t,e){return m.a._warned||(m.a._warned=!0,console.warn("<no-ssr> has been deprecated and will be removed in Nuxt 3, please use <client-only> instead")),m.a.render(t,e)}})),c.default.component(j.name,j),c.default.component("NChild",j),c.default.component(A.name,A),Object.defineProperty(c.default.prototype,"$nuxt",{get:function(){var t=this.$root.$options.$nuxt;return t||"undefined"==typeof window?t:window.$nuxt},configurable:!0}),c.default.use(f.a,{keyName:"head",attribute:"data-n-head",ssrAttribute:"data-n-head-ssr",tagIDKeyName:"hid"});var xt={name:"page",mode:"out-in",appear:!1,appearClass:"appear",appearActiveClass:"appear-active",appearToClass:"appear-to"};function wt(t){return _t.apply(this,arguments)}function _t(){return _t=Object(r.a)(regeneratorRuntime.mark((function t(e){var n,o,f,l,h,path,d,m=arguments;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return d=function(t,e){if(!t)throw new Error("inject(key, value) has no key provided");if(void 0===e)throw new Error("inject('".concat(t,"', value) has no value provided"));f[t="$"+t]=e,f.context[t]||(f.context[t]=e);var n="__nuxt_"+t+"_installed__";c.default[n]||(c.default[n]=!0,c.default.use((function(){Object.prototype.hasOwnProperty.call(c.default.prototype,t)||Object.defineProperty(c.default.prototype,t,{get:function(){return this.$root.$options[t]}})})))},n=m.length>1&&void 0!==m[1]?m[1]:{},t.next=4,C(0,n);case 4:return o=t.sent,f=gt({head:{titleTemplate:"%s - StarProApi",htmlAttrs:{lang:"en"},meta:[{charset:"utf-8"},{name:"viewport",content:"width=device-width, initial-scale=1"},{hid:"description",name:"description",content:""},{name:"format-detection",content:"telephone=no"}],link:[{rel:"icon",type:"image/x-icon",href:"/favicon.ico"}],style:[],script:[]},router:o,nuxt:{defaultTransition:xt,transitions:[xt],setTransitions:function(t){return Array.isArray(t)||(t=[t]),t=t.map((function(t){return t=t?"string"==typeof t?Object.assign({},xt,{name:t}):Object.assign({},xt,t):xt})),this.$options.nuxt.transitions=t,t},err:null,dateErr:null,error:function(t){t=t||null,f.context._errored=Boolean(t),t=t?Object(x.p)(t):null;var n=f.nuxt;return this&&(n=this.nuxt||this.$options.nuxt),n.dateErr=Date.now(),n.err=t,e&&(e.nuxt.error=t),t}}},V),l=e?e.next:function(t){return f.router.push(t)},e?h=o.resolve(e.url).route:(path=Object(x.f)(o.options.base,o.options.mode),h=o.resolve(path).route),t.next=10,Object(x.t)(f,{route:h,next:l,error:f.nuxt.error.bind(f),payload:e?e.payload:void 0,req:e?e.req:void 0,res:e?e.res:void 0,beforeRenderFns:e?e.beforeRenderFns:void 0,ssrContext:e});case 10:d("config",n),f.context.enablePreview=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};f.previewData=Object.assign({},t),d("preview",t)},t.next=15;break;case 15:return t.next=18,st(f.context,d);case 18:t.next=21;break;case 21:return t.next=24,pt(f.context);case 24:t.next=27;break;case 27:return f.context.enablePreview=function(){console.warn("You cannot call enablePreview() outside a plugin.")},t.next=30,new Promise((function(t,e){if(!o.resolve(f.context.route.fullPath).route.matched.length)return t();o.replace(f.context.route.fullPath,t,(function(n){if(!n._isRouter)return e(n);if(2!==n.type)return t();var c=o.afterEach(function(){var e=Object(r.a)(regeneratorRuntime.mark((function e(n,r){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,Object(x.j)(n);case 3:f.context.route=e.sent,f.context.params=n.params||{},f.context.query=n.query||{},c(),t();case 8:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())}))}));case 30:return t.abrupt("return",{app:f,router:o});case 31:case"end":return t.stop()}}),t)}))),_t.apply(this,arguments)}},340:function(t,e,n){"use strict";n(235)},341:function(t,e,n){"use strict";n(236)},343:function(t,e,n){},434:function(t,e){}},[[277,3,1,4]]]);