<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoChatDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoChat" >
        <result column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="toid" property="toid" />
        <result column="created" property="created" />
        <result column="lastTime" property="lastTime" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="pic" property="pic" />
        <result column="ban" property="ban" />
        <result column="myUnRead" property="myUnRead" />
        <result column="otherUnRead" property="otherUnRead" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `uid`,
        `toid`,
        `created`,
        `lastTime`,
        `type`,
        `name`,
        `pic`,
        `ban`,
        `myUnRead`,
        `otherUnRead`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoChat"  keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ${prefix}_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != toid'>
                `toid`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != lastTime'>
                `lastTime`,
            </if>
            <if test ='null != type'>
                `type`,
            </if>
            <if test ='null != name'>
                `name`,
            </if>
            <if test ='null != pic'>
                `pic`,
            </if>
            <if test ='null != ban'>
                `ban`,
            </if>
            <if test ='null != myUnRead'>
                `myUnRead`,
            </if>
            <if test ='null != otherUnRead'>
                `otherUnRead`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != toid'>
                #{toid},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != lastTime'>
                #{lastTime},
            </if>
            <if test ='null != type'>
                #{type},
            </if>
            <if test ='null != name'>
                #{name},
            </if>
            <if test ='null != pic'>
                #{pic},
            </if>
            <if test ='null != ban'>
                #{ban},
            </if>
            <if test ='null != myUnRead'>
                #{myUnRead},
            </if>
            <if test ='null != otherUnRead'>
                #{otherUnRead}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_chat ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.uid},
                #{curr.toid},
                #{curr.created},
                #{curr.lastTime},
                #{curr.type},
                #{curr.name},
                #{curr.pic},
                #{curr.ban}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoChat">
        UPDATE ${prefix}_chat
        <set>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != toid'>`toid` = #{toid},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != lastTime'>`lastTime` = #{lastTime},</if>
            <if test ='null != type'>`type` = #{type},</if>
            <if test ='null != name'>`name` = #{name},</if>
            <if test ='null != pic'>`pic` = #{pic},</if>
            <if test ='null != ban'>`ban` = #{ban},</if>
            <if test ='null != myUnRead'>`myUnRead` = #{myUnRead},</if>
            <if test ='null != otherUnRead'>`otherUnRead` = #{otherUnRead}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_chat
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_chat WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_chat
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_chat
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != toid'>
                and `toid` = #{toid}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != lastTime'>
                and `lastTime` = #{lastTime}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
        </where>
        order by created desc
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_chat
        <where>
            <if test ='null != typechoChat.id'>
                and `id` = #{typechoChat.id}
            </if>
            <if test ='null != typechoChat.uid'>
                and (`uid` = #{typechoChat.uid} or `toid` = #{typechoChat.uid})
            </if>
            <if test ='null != typechoChat.toid'>
                and (`toid` = #{typechoChat.toid} or `uid` = #{typechoChat.toid})
            </if>
            <if test ='null != typechoChat.created'>
                and `created` = #{typechoChat.created}
            </if>
            <if test ='null != typechoChat.lastTime'>
                and `lastTime` = #{typechoChat.lastTime}
            </if>
            <if test ='null != typechoChat.type'>
                and `type` = #{typechoChat.type}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`name`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_chat
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != toid'>
                and `toid` = #{toid}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != lastTime'>
                and `lastTime` = #{lastTime}
            </if>
            <if test ='null != type'>
                and `type` = #{type}
            </if>
        </where>
    </select>
</mapper>