<!doctype html>
<html data-n-head-ssr lang="en" data-n-head="%7B%22lang%22:%7B%22ssr%22:%22en%22%7D%7D">
<head>
    <title>烟圈API - 热爱你的热爱</title>
    <meta data-n-head="ssr" charset="utf-8">
    <meta data-n-head="ssr" name="viewport" content="width=device-width,initial-scale=1">
    <meta data-n-head="ssr" data-hid="description" name="description" content="">
    <meta data-n-head="ssr" name="format-detection" content="telephone=no">

    <!-- 移动端状态栏优化 -->
    <meta name="theme-color" content="#0f172a">
    <meta name="msapplication-navbutton-color" content="#0f172a">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="烟圈API">

    <!-- Android Chrome -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="烟圈API">

    <!-- Windows Phone -->
    <meta name="msapplication-TileColor" content="#0f172a">
    <meta name="msapplication-TileImage" content="/favicon.ico">

    <!-- PWA支持 -->
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- 防止用户缩放 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <link data-n-head="ssr" rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            min-height: 100dvh; /* 动态视口高度，iOS Safari友好 */
            position: relative;
            overflow-x: hidden;
            /* iOS Safari兼容的背景设置 */
            background-color: #0f172a;
            background-image:
                -webkit-linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%),
                -webkit-radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
                -webkit-radial-gradient(circle at 80% 20%, rgba(74, 222, 128, 0.08) 0%, transparent 50%);
            background-image:
                linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%),
                radial-gradient(circle at 20% 80%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(74, 222, 128, 0.08) 0%, transparent 50%);
            background-size: 100% 100%, 100% 100%, 100% 100%;
            background-repeat: no-repeat;
            background-attachment: scroll; /* iOS Safari不支持fixed */
        }

        /* 桌面端增强效果 */
        @media (min-width: 768px) and (prefers-reduced-motion: no-preference) {
            body::before {
                content: '';
                position: absolute; /* 改为absolute，避免iOS Safari的fixed问题 */
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 40% 40%, rgba(22, 163, 74, 0.06) 0%, transparent 50%);
                animation: modernGlow 8s ease-in-out infinite alternate;
                z-index: -1;
                pointer-events: none;
            }
        }

        /* iOS Safari专用优化 */
        @supports (-webkit-touch-callout: none) {
            body {
                /* iOS Safari专用背景 */
                background: #0f172a;
                background: -webkit-linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
                background-size: 100% 100%;
                background-attachment: scroll;
            }

            /* 移除伪元素，直接使用多重背景 */
            body::before {
                display: none;
            }
        }

        @keyframes modernGlow {
            0% {
                opacity: 0.6;
                transform: scale(1) rotate(0deg);
            }
            100% {
                opacity: 0.9;
                transform: scale(1.05) rotate(1deg);
            }
        }

        .container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .index-main {
            text-align: center;
            padding: 60px 40px;
            max-width: 500px;
            width: 100%;
            position: relative;
        }



        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 80px;
            gap: 40px;
        }

        .api-logo {
            width: 120px;
            height: 120px;
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4))
                    drop-shadow(0 0 20px rgba(34, 197, 94, 0.4))
                    drop-shadow(0 0 40px rgba(74, 222, 128, 0.3));
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: logoBreathing 3s ease-in-out infinite;
            transform-origin: center center;
        }

        .api-logo:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.5))
                    drop-shadow(0 0 40px rgba(34, 197, 94, 0.6))
                    drop-shadow(0 0 80px rgba(74, 222, 128, 0.5));
            animation-play-state: paused;
        }

        @keyframes logoBreathing {
            0%, 100% {
                transform: scale(1);
                filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4))
                        drop-shadow(0 0 20px rgba(34, 197, 94, 0.4))
                        drop-shadow(0 0 40px rgba(74, 222, 128, 0.3));
            }
            25% {
                transform: scale(1.03);
                filter: drop-shadow(0 5px 14px rgba(0, 0, 0, 0.45))
                        drop-shadow(0 0 25px rgba(34, 197, 94, 0.5))
                        drop-shadow(0 0 50px rgba(74, 222, 128, 0.35));
            }
            50% {
                transform: scale(1.08);
                filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.6))
                        drop-shadow(0 0 40px rgba(34, 197, 94, 0.7))
                        drop-shadow(0 0 80px rgba(74, 222, 128, 0.5))
                        drop-shadow(0 0 120px rgba(22, 163, 74, 0.3));
            }
            75% {
                transform: scale(1.03);
                filter: drop-shadow(0 5px 14px rgba(0, 0, 0, 0.45))
                        drop-shadow(0 0 25px rgba(34, 197, 94, 0.5))
                        drop-shadow(0 0 50px rgba(74, 222, 128, 0.35));
            }
        }



        .star-button1 {
            background: rgba(34, 197, 94, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #ffffff;
            padding: 18px 36px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-block;
            position: relative;
            overflow: hidden;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(34, 197, 94, 0.2),
                inset 0 1px 0 rgba(74, 222, 128, 0.3),
                inset 0 -1px 0 rgba(22, 163, 74, 0.2);
        }

        .star-button1::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.5s ease;
        }

        .star-button1:hover {
            background: rgba(34, 197, 94, 0.25);
            border-color: rgba(34, 197, 94, 0.5);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 16px 50px rgba(0, 0, 0, 0.4),
                0 0 40px rgba(34, 197, 94, 0.4),
                0 0 80px rgba(74, 222, 128, 0.3),
                inset 0 1px 0 rgba(74, 222, 128, 0.4),
                inset 0 -1px 0 rgba(22, 163, 74, 0.3);
        }

        .star-button1:hover::before {
            left: 100%;
        }

        .star-button1:hover::before {
            left: 100%;
        }

        .star-button1:active {
            transform: translateY(0);
        }
        
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(15, 15, 35, 0.8);
            backdrop-filter: blur(12px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; backdrop-filter: blur(0px); }
            to { opacity: 1; backdrop-filter: blur(12px); }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                backdrop-filter: blur(25px);
            }
        }

        .modal {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(30px);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 28px;
            padding: 40px;
            width: 90%;
            max-width: 460px;
            box-shadow:
                0 30px 100px rgba(0, 0, 0, 0.5),
                0 10px 40px rgba(0, 0, 0, 0.3),
                0 0 50px rgba(34, 197, 94, 0.15),
                inset 0 1px 0 rgba(74, 222, 128, 0.3),
                inset 0 -1px 0 rgba(22, 163, 74, 0.1);
            color: #ffffff;
            position: relative;
            animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 24px;
            padding: 1px;
            background: linear-gradient(135deg,
                rgba(255,255,255,0.25) 0%,
                rgba(255,255,255,0.1) 25%,
                rgba(255,255,255,0.05) 50%,
                rgba(255,255,255,0.1) 75%,
                rgba(255,255,255,0.2) 100%);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            pointer-events: none;
        }

        .modal::after {
            content: '';
            position: absolute;
            top: 1px;
            left: 1px;
            right: 1px;
            bottom: 1px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 23px;
            pointer-events: none;
        }
        .modal-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .modal-content {
            margin-bottom: 28px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 18px 20px;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 18px;
            font-size: 16px;
            color: #ffffff;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(34, 197, 94, 0.5);
            background: rgba(15, 23, 42, 0.8);
            box-shadow:
                0 0 0 4px rgba(34, 197, 94, 0.1),
                0 0 30px rgba(34, 197, 94, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(74, 222, 128, 0.3);
            transform: translateY(-2px);
        }
        .modal-footer {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 24px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-cancel {
            background: rgba(30, 41, 59, 0.6);
            color: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(34, 197, 94, 0.15);
            backdrop-filter: blur(15px);
        }

        .btn-cancel:hover {
            background: rgba(30, 41, 59, 0.8);
            color: rgba(255, 255, 255, 0.9);
            border-color: rgba(34, 197, 94, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 197, 94, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(74, 222, 128, 0.8) 100%);
            color: white;
            border: 1px solid rgba(34, 197, 94, 0.3);
            backdrop-filter: blur(15px);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(74, 222, 128, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(34, 197, 94, 0.4), 0 0 25px rgba(74, 222, 128, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.8) 0%, rgba(220, 38, 38, 0.8) 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
        }

        .btn-blue {
            background: linear-gradient(135deg, rgba(22, 163, 74, 0.8) 0%, rgba(34, 197, 94, 0.8) 100%);
            color: white;
            border: 1px solid rgba(22, 163, 74, 0.3);
            backdrop-filter: blur(15px);
        }

        .btn-blue:hover {
            background: linear-gradient(135deg, rgba(22, 163, 74, 0.9) 0%, rgba(34, 197, 94, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(22, 163, 74, 0.4), 0 0 25px rgba(34, 197, 94, 0.3);
        }
        .error-message {
            background: rgba(239, 68, 68, 0.15);
            color: #fca5a5;
            font-size: 14px;
            margin-top: 16px;
            text-align: center;
            padding: 14px 18px;
            border-radius: 14px;
            border: 1px solid rgba(239, 68, 68, 0.3);
            backdrop-filter: blur(15px);
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(34, 197, 94, 0.15);
            color: #86efac;
            font-size: 14px;
            margin-top: 16px;
            text-align: center;
            padding: 14px 18px;
            border-radius: 14px;
            border: 1px solid rgba(34, 197, 94, 0.3);
            backdrop-filter: blur(15px);
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.2);
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(2px);
            border-radius: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .index-main {
                padding: 40px 24px;
                margin: 20px;
            }

            .star-logo1 {
                font-size: 42px;
            }

            .modal {
                padding: 24px;
                margin: 20px;
            }

            .modal-footer {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .star-logo1 {
                font-size: 36px;
            }

            .star-button1 {
                padding: 14px 28px;
                font-size: 15px;
            }
        }

        /* 进度条样式 */
        .progress-container {
            margin: 24px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #7c77c6 0%, #6366f1 50%, #3b82f6 100%);
            border-radius: 8px;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            width: 0%;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShine 1.5s infinite;
        }

        @keyframes progressShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 12px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* 加载动画 */
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 24px 0;
        }

        .spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(120, 119, 198, 0.2);
            border-top: 3px solid #7c77c6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
            backdrop-filter: blur(10px);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* 与admin一致的按钮样式 */
        .admin-btn {
            background: rgba(34, 197, 94, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #ffffff;
            padding: 18px 36px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(34, 197, 94, 0.2),
                inset 0 1px 0 rgba(74, 222, 128, 0.3),
                inset 0 -1px 0 rgba(22, 163, 74, 0.2);
            min-width: 200px;
        }

        .admin-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.5s ease;
        }

        .admin-btn:hover {
            background: rgba(34, 197, 94, 0.25);
            border-color: rgba(34, 197, 94, 0.5);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 16px 50px rgba(0, 0, 0, 0.4),
                0 0 40px rgba(34, 197, 94, 0.4),
                0 0 80px rgba(74, 222, 128, 0.3),
                inset 0 1px 0 rgba(74, 222, 128, 0.4),
                inset 0 -1px 0 rgba(22, 163, 74, 0.3);
        }

        .admin-btn:hover::before {
            left: 100%;
        }

        .admin-btn:active {
            transform: translateY(0);
        }

        /* 密钥输入框样式 */
        .key-input {
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 12px;
            color: #ffffff;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 16px;
            font-size: 16px;
            text-align: center;
            min-width: 200px;
            margin-bottom: 20px;
        }

        .key-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        .key-input:focus {
            outline: none;
            border-color: rgba(34, 197, 94, 0.5);
            background: rgba(15, 23, 42, 0.8);
            box-shadow:
                0 0 0 4px rgba(34, 197, 94, 0.1),
                0 0 30px rgba(34, 197, 94, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(74, 222, 128, 0.3);
            transform: translateY(-2px);
        }

        /* 密钥验证区域 */
        .key-verification {
            display: none;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .key-verification.show {
            display: flex;
        }

        /* 错误提示样式 */
        .error-message {
            background: rgba(239, 68, 68, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 12px;
            padding: 12px 20px;
            color: #ffffff;
            font-size: 14px;
            margin-top: 10px;
            animation: errorShake 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes errorShake {
            0%, 100% {
                transform: translateX(0);
            }
            25% {
                transform: translateX(-3px);
            }
            75% {
                transform: translateX(3px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="index-main">
            <div class="logo-container">
                <img src="logo.png" alt="烟圈API Logo" class="api-logo" />

                <!-- 维护系统按钮 -->
                <button class="admin-btn" id="maintenanceBtn" onclick="showTimeKeyVerification()">维护系统</button>

                <!-- 时间密钥验证区域 -->
                <div class="key-verification" id="timeKeyVerification">
                    <input type="text" class="key-input" id="timeKeyInput" placeholder="请输入密钥" maxlength="4" />
                    <button class="admin-btn" onclick="verifyTimeKey()">验证</button>
                    <div id="timeKeyErrorMessage"></div>
                </div>
            </div>
        </div>
    </div>



    <!-- 第一步安装弹窗 -->
    <div id="firstInstallModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">开始第一步安装</div>
            </div>
            <div class="modal-content">
                <div id="installDescription" style="color: rgba(255, 255, 255, 0.8); font-size: 14px; text-align: center; line-height: 1.5;">
                    确定要开始安装吗？安装会对数据库造成修改，请在安装前备份好数据！
                </div>

                <!-- 进度条 -->
                <div id="installProgressContainer" class="progress-container">
                    <div class="progress-bar">
                        <div id="installProgressFill" class="progress-fill"></div>
                    </div>
                    <div id="installProgressText" class="progress-text">准备安装...</div>
                </div>

                <!-- 加载动画 -->
                <div id="installLoadingSpinner" class="loading-spinner">
                    <div class="spinner"></div>
                    <div id="installLoadingText" class="loading-text">正在处理，请稍候...</div>
                </div>

                <div id="installErrorMessage" class="error-message" style="display: none;"></div>
                <div id="installSuccessMessage" class="success-message" style="display: none;"></div>
            </div>
            <div id="installModalFooter" class="modal-footer">
                <button class="btn btn-danger" onclick="startInstall()">开始安装</button>
                <button class="btn btn-primary" onclick="updateOnly()">仅更新</button>
                <button class="btn btn-blue" onclick="hideFirstInstall()">再等等</button>
            </div>
        </div>
    </div>

    <!-- 第二步安装弹窗 -->
    <div id="secondInstallModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <div class="modal-title">进行第二步安装</div>
            </div>
            <div class="modal-content">
                <div id="secondInstallDescription" style="color: #666; font-size: 14px; text-align: center;">
                    第一步安装完成，是否继续进行第二步安装？
                </div>

                <!-- 进度条 -->
                <div id="secondInstallProgressContainer" class="progress-container">
                    <div class="progress-bar">
                        <div id="secondInstallProgressFill" class="progress-fill"></div>
                    </div>
                    <div id="secondInstallProgressText" class="progress-text">准备第二步安装...</div>
                </div>

                <!-- 加载动画 -->
                <div id="secondInstallLoadingSpinner" class="loading-spinner">
                    <div class="spinner"></div>
                    <div id="secondInstallLoadingText" class="loading-text">正在处理，请稍候...</div>
                </div>

                <div id="secondInstallErrorMessage" class="error-message" style="display: none;"></div>
                <div id="secondInstallSuccessMessage" class="success-message" style="display: none;"></div>
            </div>
            <div id="secondInstallModalFooter" class="modal-footer">
                <button class="btn btn-cancel" onclick="hideSecondInstall()">取消</button>
                <button class="btn btn-primary" onclick="startSecondInstall()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 动态设置状态栏颜色
        function updateStatusBarColor() {
            const themeColorMeta = document.querySelector('meta[name="theme-color"]');
            const msNavButtonMeta = document.querySelector('meta[name="msapplication-navbutton-color"]');
            const tileColorMeta = document.querySelector('meta[name="msapplication-TileColor"]');

            // 根据页面状态动态调整颜色
            const baseColor = '#0f172a';
            const modalColor = '#1e293b';

            // 检查是否有弹窗打开
            const isModalOpen = document.querySelector('.modal-overlay[style*="flex"]') ||
                               document.querySelector('.modal-overlay[style*="block"]');

            const currentColor = isModalOpen ? modalColor : baseColor;

            if (themeColorMeta) themeColorMeta.setAttribute('content', currentColor);
            if (msNavButtonMeta) msNavButtonMeta.setAttribute('content', currentColor);
            if (tileColorMeta) tileColorMeta.setAttribute('content', currentColor);
        }

        // 页面加载时设置状态栏颜色
        document.addEventListener('DOMContentLoaded', updateStatusBarColor);

        let verifiedKey = '';

        // 显示时间密钥验证区域
        function showTimeKeyVerification() {
            const maintenanceBtn = document.getElementById('maintenanceBtn');
            const timeKeyVerification = document.getElementById('timeKeyVerification');
            const timeKeyInput = document.getElementById('timeKeyInput');

            maintenanceBtn.style.display = 'none';
            timeKeyVerification.classList.add('show');
            timeKeyInput.focus();
        }

        // 隐藏时间密钥验证区域
        function hideTimeKeyVerification() {
            const maintenanceBtn = document.getElementById('maintenanceBtn');
            const timeKeyVerification = document.getElementById('timeKeyVerification');
            const timeKeyInput = document.getElementById('timeKeyInput');
            const errorMessage = document.getElementById('timeKeyErrorMessage');

            timeKeyVerification.classList.remove('show');
            maintenanceBtn.style.display = 'block';
            timeKeyInput.value = '';
            errorMessage.innerHTML = '';
        }

        // 验证时间密钥
        function verifyTimeKey() {
            const timeKeyInput = document.getElementById('timeKeyInput');
            const errorMessage = document.getElementById('timeKeyErrorMessage');
            const inputKey = timeKeyInput.value.trim();

            // 发送验证请求到服务器
            fetch('/systemStarPro/verifyTimeKey', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'key=' + encodeURIComponent(inputKey)
            })
            .then(response => response.text())
            .then(data => {
                if (data.trim() === 'success') {
                    // 时间密钥验证成功，显示访问密钥输入
                    showAccessKeyVerification();
                } else if (data.trim() === 'locked') {
                    // 账户被锁定
                    showTimeKeyError('locked');
                } else {
                    // 验证失败，显示错误信息
                    showTimeKeyError('密钥错误');

                    // 清空输入框
                    timeKeyInput.value = '';
                    timeKeyInput.focus();
                }
            })
            .catch(error => {
                console.error('验证时间密钥失败:', error);
                showTimeKeyError('验证失败，请重试');
            });
        }

        // 显示访问密钥验证弹窗
        function showAccessKeyVerification() {
            // 隐藏时间密钥验证区域
            hideTimeKeyVerification();

            // 显示访问密钥弹窗
            const accessKeyModal = document.createElement('div');
            accessKeyModal.className = 'modal-overlay';
            accessKeyModal.id = 'accessKeyModal';
            accessKeyModal.innerHTML = `
                <div class="modal">
                    <div class="modal-header">
                        <div class="modal-title">第二步验证</div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">请输入访问密钥以继续维护</div>
                    </div>
                    <div class="modal-content">
                        <div class="form-group">
                            <input type="password" id="accessKey" class="form-input" placeholder="请输入访问密钥" />
                        </div>
                        <div id="accessKeyErrorMessage" class="error-message" style="display: none;"></div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-cancel" onclick="hideAccessKeyVerification()">取消</button>
                        <button class="btn btn-primary" onclick="verifyAccessKey()">验证</button>
                    </div>
                </div>
            `;
            document.body.appendChild(accessKeyModal);
            accessKeyModal.style.display = 'flex';
            document.getElementById('accessKey').focus();
            updateStatusBarColor();
        }

        // 隐藏访问密钥验证弹窗
        function hideAccessKeyVerification() {
            const accessKeyModal = document.getElementById('accessKeyModal');
            if (accessKeyModal) {
                accessKeyModal.remove();
            }
            // 显示维护系统按钮
            document.getElementById('maintenanceBtn').style.display = 'block';
            updateStatusBarColor();
        }

        // 验证访问密钥
        function verifyAccessKey() {
            const key = document.getElementById('accessKey').value.trim();
            const errorDiv = document.getElementById('accessKeyErrorMessage');

            if (!key) {
                showError(errorDiv, '请输入访问密钥');
                return;
            }

            // 设置加载状态
            const modal = document.querySelector('#accessKeyModal .modal');
            modal.classList.add('loading');

            // 调用原来的密钥验证接口
            fetch('/systemStarPro/isKey', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(key)
            })
            .then(response => response.json())
            .then(data => {
                modal.classList.remove('loading');

                if (data.code === 1) {
                    // KEY验证成功
                    verifiedKey = key;
                    hideAccessKeyVerification();
                    showFirstInstall();
                } else {
                    // 密钥验证失败
                    showError(errorDiv, data.msg || '访问密钥验证失败，请检查密钥是否正确');
                }
            })
            .catch(error => {
                modal.classList.remove('loading');
                console.error('验证访问密钥时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示时间密钥错误信息
        function showTimeKeyError(message) {
            const errorMessage = document.getElementById('timeKeyErrorMessage');
            const timeKeyInput = document.getElementById('timeKeyInput');
            const verifyButton = document.querySelector('.key-verification button');

            if (message === 'locked') {
                errorMessage.innerHTML = '<div class="error-message">尝试次数过多，请5分钟后再试</div>';

                // 禁用输入框和按钮
                timeKeyInput.disabled = true;
                verifyButton.disabled = true;

                // 5分钟后重新启用
                setTimeout(() => {
                    timeKeyInput.disabled = false;
                    verifyButton.disabled = false;
                    errorMessage.innerHTML = '';
                }, 300000); // 5分钟
            } else {
                errorMessage.innerHTML = '<div class="error-message">' + message + '</div>';

                // 3秒后自动隐藏
                setTimeout(() => {
                    errorMessage.innerHTML = '';
                }, 3000);
            }
        }

        // 显示第一步安装弹窗
        function showFirstInstall() {
            document.getElementById('firstInstallModal').style.display = 'flex';
        }

        // 隐藏第一步安装弹窗
        function hideFirstInstall() {
            document.getElementById('firstInstallModal').style.display = 'none';
            document.getElementById('installErrorMessage').style.display = 'none';
            document.getElementById('installSuccessMessage').style.display = 'none';

            // 重置界面状态
            document.getElementById('installDescription').style.display = 'block';
            document.getElementById('installModalFooter').style.display = 'flex';
            hideProgress('installProgressContainer');
            hideLoadingSpinner('installLoadingSpinner');
        }

        // 显示进度
        function showProgress(containerId, fillId, textId, text, progress) {
            document.getElementById(containerId).style.display = 'block';
            document.getElementById(fillId).style.width = progress + '%';
            document.getElementById(textId).textContent = text;
        }

        // 隐藏进度
        function hideProgress(containerId) {
            document.getElementById(containerId).style.display = 'none';
        }

        // 显示加载动画
        function showLoadingSpinner(spinnerId, textId, text) {
            document.getElementById(spinnerId).style.display = 'block';
            document.getElementById(textId).textContent = text;
        }

        // 隐藏加载动画
        function hideLoadingSpinner(spinnerId) {
            document.getElementById(spinnerId).style.display = 'none';
        }

        // 开始安装
        function startInstall() {
            const errorDiv = document.getElementById('installErrorMessage');
            const successDiv = document.getElementById('installSuccessMessage');
            const descriptionDiv = document.getElementById('installDescription');
            const footerDiv = document.getElementById('installModalFooter');

            // 隐藏描述和按钮
            descriptionDiv.style.display = 'none';
            footerDiv.style.display = 'none';
            hideMessage(errorDiv);
            hideMessage(successDiv);

            // 显示进度条
            showProgress('installProgressContainer', 'installProgressFill', 'installProgressText', '正在初始化安装...', 10);

            // 模拟安装进度
            let progress = 10;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                let progressText = '正在安装数据库表...';
                if (progress > 30) progressText = '正在导入基础数据...';
                if (progress > 60) progressText = '正在配置系统参数...';
                if (progress > 80) progressText = '正在完成安装...';

                showProgress('installProgressContainer', 'installProgressFill', 'installProgressText', progressText, progress);
            }, 500);

            fetch('/installStar/newInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.text())
            .then(data => {
                clearInterval(progressInterval);

                // 检查是否是重复安装的提示
                if (data.includes('虽然重复执行也没关系') || data.includes('1分钟后再来操作')) {
                    hideProgress('installProgressContainer');
                    descriptionDiv.style.display = 'block';
                    footerDiv.style.display = 'flex';
                    showError(errorDiv, '系统检测到您刚刚执行过安装操作，请稍后再试');
                    return;
                }

                if (data.includes('执行结束') || data.includes('安装执行完成')) {
                    // 完成进度条
                    showProgress('installProgressContainer', 'installProgressFill', 'installProgressText', '第一步安装完成！', 100);

                    // 检查是否是真正的新安装还是重复安装
                    const isRealInstall = data.includes('添加完成') || data.includes('创建完成');
                    const successMessage = isRealInstall ?
                        '第一步安装完成！即将进入第二步安装...' :
                        '系统检查完成！所有组件都已是最新状态，即将进入第二步...';

                    setTimeout(() => {
                        hideProgress('installProgressContainer');
                        showSuccess(successDiv, successMessage);

                        setTimeout(() => {
                            hideFirstInstall();
                            showSecondInstall();
                        }, 1500);
                    }, 1000);
                } else {
                    hideProgress('installProgressContainer');
                    descriptionDiv.style.display = 'block';
                    footerDiv.style.display = 'flex';
                    showError(errorDiv, data || '安装失败，请稍后重试');
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                hideProgress('installProgressContainer');
                descriptionDiv.style.display = 'block';
                footerDiv.style.display = 'flex';
                console.error('安装时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 仅更新
        function updateOnly() {
            const errorDiv = document.getElementById('installErrorMessage');
            const successDiv = document.getElementById('installSuccessMessage');
            const descriptionDiv = document.getElementById('installDescription');
            const footerDiv = document.getElementById('installModalFooter');

            // 隐藏描述和按钮
            descriptionDiv.style.display = 'none';
            footerDiv.style.display = 'none';
            hideMessage(errorDiv);
            hideMessage(successDiv);

            // 显示加载动画
            showLoadingSpinner('installLoadingSpinner', 'installLoadingText', '正在更新系统，请稍候...');

            fetch('/installStar/proInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingSpinner('installLoadingSpinner');

                if (data.code === 1) {
                    showSuccess(successDiv, '更新完成！');
                    setTimeout(() => {
                        hideFirstInstall();
                    }, 2000);
                } else {
                    descriptionDiv.style.display = 'block';
                    footerDiv.style.display = 'flex';
                    showError(errorDiv, data.msg || '更新失败，请稍后重试');
                }
            })
            .catch(error => {
                hideLoadingSpinner('installLoadingSpinner');
                descriptionDiv.style.display = 'block';
                footerDiv.style.display = 'flex';
                console.error('更新时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示第二步安装弹窗
        function showSecondInstall() {
            document.getElementById('secondInstallModal').style.display = 'flex';
        }

        // 隐藏第二步安装弹窗
        function hideSecondInstall() {
            document.getElementById('secondInstallModal').style.display = 'none';
            document.getElementById('secondInstallErrorMessage').style.display = 'none';
            document.getElementById('secondInstallSuccessMessage').style.display = 'none';

            // 重置界面状态
            document.getElementById('secondInstallDescription').style.display = 'block';
            document.getElementById('secondInstallModalFooter').style.display = 'flex';
            hideProgress('secondInstallProgressContainer');
            hideLoadingSpinner('secondInstallLoadingSpinner');
        }

        // 开始第二步安装
        function startSecondInstall() {
            const errorDiv = document.getElementById('secondInstallErrorMessage');
            const successDiv = document.getElementById('secondInstallSuccessMessage');
            const descriptionDiv = document.getElementById('secondInstallDescription');
            const footerDiv = document.getElementById('secondInstallModalFooter');

            // 隐藏描述和按钮
            descriptionDiv.style.display = 'none';
            footerDiv.style.display = 'none';
            hideMessage(errorDiv);
            hideMessage(successDiv);

            // 显示进度条
            showProgress('secondInstallProgressContainer', 'secondInstallProgressFill', 'secondInstallProgressText', '正在进行第二步安装...', 20);

            // 模拟第二步安装进度
            let progress = 20;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;

                let progressText = '正在安装扩展功能...';
                if (progress > 50) progressText = '正在配置高级选项...';
                if (progress > 80) progressText = '正在完成第二步安装...';

                showProgress('secondInstallProgressContainer', 'secondInstallProgressFill', 'secondInstallProgressText', progressText, progress);
            }, 400);

            fetch('/installStar/proInstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'webkey=' + encodeURIComponent(verifiedKey)
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);

                if (data.code === 1) {
                    // 完成进度条
                    showProgress('secondInstallProgressContainer', 'secondInstallProgressFill', 'secondInstallProgressText', '第二步安装完成！', 100);

                    setTimeout(() => {
                        hideProgress('secondInstallProgressContainer');
                        showSuccess(successDiv, '🎉 恭喜！所有安装步骤已完成，系统已准备就绪！');

                        setTimeout(() => {
                            hideSecondInstall();
                        }, 3000);
                    }, 1000);
                } else {
                    hideProgress('secondInstallProgressContainer');
                    descriptionDiv.style.display = 'block';
                    footerDiv.style.display = 'flex';
                    showError(errorDiv, data.msg || '第二步安装失败，请稍后重试');
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                hideProgress('secondInstallProgressContainer');
                descriptionDiv.style.display = 'block';
                footerDiv.style.display = 'flex';
                console.error('第二步安装时发生错误:', error);
                showError(errorDiv, '网络错误，请稍后重试');
            });
        }

        // 显示错误信息
        function showError(element, message) {
            element.textContent = message;
            element.style.display = 'block';
        }

        // 显示成功信息
        function showSuccess(element, message) {
            element.textContent = message;
            element.style.display = 'block';
        }

        // 隐藏信息
        function hideMessage(element) {
            element.style.display = 'none';
        }

        // 支持回车键提交
        document.addEventListener('DOMContentLoaded', function() {
            // 时间密钥输入框回车键支持
            document.getElementById('timeKeyInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyTimeKey();
                }
            });
        });

        // 动态添加的访问密钥输入框回车键支持
        document.addEventListener('keypress', function(e) {
            if (e.target.id === 'accessKey' && e.key === 'Enter') {
                verifyAccessKey();
            }
        });

        // 点击遮罩关闭弹窗
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    overlay.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
