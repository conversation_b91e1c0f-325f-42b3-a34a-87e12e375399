<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.StarProApi.dao.TypechoForumCommentDao">

    <resultMap id="BaseResultMap" type="com.StarProApi.entity.TypechoForumComment" >
        <result column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="created" property="created" />
        <result column="forumid" property="forumid" />
        <result column="likes" property="likes" />
        <result column="text" property="text" />
        <result column="parent" property="parent" />
        <result column="section" property="section" />
        <result column="status" property="status" />
        <result column="pic" property="pic" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `uid`,
        `created`,
        `forumid`,
        `likes`,
        `text`,
        `parent`,
        `section`,
        `pic`,
        `status`
    </sql>

    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.StarProApi.entity.TypechoForumComment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ${prefix}_forum_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                `id`,
            </if>
            <if test ='null != uid'>
                `uid`,
            </if>
            <if test ='null != created'>
                `created`,
            </if>
            <if test ='null != forumid'>
                `forumid`,
            </if>
            <if test ='null != likes'>
                `likes`,
            </if>
            <if test ='null != text'>
                `text`,
            </if>
            <if test ='null != parent'>
                `parent`,
            </if>
            <if test ='null != section'>
                `section`,
            </if>
            <if test ='null != pic'>
                `pic`,
            </if>
            <if test ='null != status'>
                `status`
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != id'>
                #{id},
            </if>
            <if test ='null != uid'>
                #{uid},
            </if>
            <if test ='null != created'>
                #{created},
            </if>
            <if test ='null != forumid'>
                #{forumid},
            </if>
            <if test ='null != likes'>
                #{likes},
            </if>
            <if test ='null != text'>
                #{text},
            </if>
            <if test ='null != parent'>
                #{parent},
            </if>
            <if test ='null != section'>
                #{section},
            </if>
            <if test ='null != pic'>
                #{pic},
            </if>
            <if test ='null != status'>
                #{status}
            </if>
        </trim>
    </insert>

    <!-- 批量插入数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${prefix}_forum_comment ( <include refid="Base_Column_List" /> ) VALUES
        <foreach collection="list" item="curr" index="index" separator=",">
            (
                #{curr.id},
                #{curr.uid},
                #{curr.created},
                #{curr.forumid},
                #{curr.likes},
                #{curr.text},
                #{curr.pic},
                #{curr.parent}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.StarProApi.entity.TypechoForumComment">
        UPDATE ${prefix}_forum_comment
        <set>
            <if test ='null != uid'>`uid` = #{uid},</if>
            <if test ='null != created'>`created` = #{created},</if>
            <if test ='null != forumid'>`forumid` = #{forumid},</if>
            <if test ='null != likes'>`likes` = #{likes},</if>
            <if test ='null != text'>`text` = #{text},</if>
            <if test ='null != parent'>`parent` = #{parent},</if>
            <if test ='null != section'>`section` = #{section},</if>
            <if test ='null != pic'>`pic` = #{pic},</if>
            <if test ='null != status'>`status` = #{status}</if>
        </set>
        WHERE `id` = #{id}
    </update>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM ${prefix}_forum_comment
        WHERE `id` = #{key}
    </delete>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType = "java.util.List">
        DELETE FROM ${prefix}_forum_comment WHERE id IN
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <!-- 主键查询 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_comment
        WHERE `id` = #{key}
    </select>

    <!-- 条件查询 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_comment
        <where>
            <if test ='null != id'>
                and `id` = #{id}
            </if>
            <if test ='null != uid'>
                and `uid` = #{uid}
            </if>
            <if test ='null != created'>
                and `created` = #{created}
            </if>
            <if test ='null != forumid'>
                and `forumid` = #{forumid}
            </if>
            <if test ='null != likes'>
                and `likes` = #{likes}
            </if>
            <if test ='null != text'>
                and `text` = #{text}
            </if>
            <if test ='null != parent'>
                and `parent` = #{parent}
            </if>
            <if test ='null != section'>
                and `section` = #{section}
            </if>
            <if test ='null != pic'>
                and `pic` = #{pic}
            </if>
            <if test ='null != status'>
                and `status` = #{status}
            </if>
        </where>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${prefix}_forum_comment
        <where>
            <if test ='null != typechoForumComment.id'>
                and `id` = #{typechoForumComment.id}
            </if>
            <if test ='null != typechoForumComment.uid'>
                and `uid` = #{typechoForumComment.uid}
            </if>
            <if test ='null != typechoForumComment.created'>
                and `created` = #{typechoForumComment.created}
            </if>
            <if test ='null != typechoForumComment.forumid'>
                and `forumid` = #{typechoForumComment.forumid}
            </if>
            <if test ='null != typechoForumComment.likes'>
                and `likes` = #{typechoForumComment.likes}
            </if>
            <if test ='null != typechoForumComment.text'>
                and `text` = #{typechoForumComment.text}
            </if>
            <if test ='null != typechoForumComment.parent'>
                and `parent` = #{typechoForumComment.parent}
            </if>
            <if test ='null != typechoForumComment.section'>
                and `section` = #{typechoForumComment.section}
            </if>
            <if test ='null != typechoForumComment.pic'>
                and `pic` = #{typechoForumComment.pic}
            </if>
            <if test ='null != typechoForumComment.status'>
                and `status` = #{typechoForumComment.status}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
        <if test ='"" != order'>
            order by ${order} desc
        </if>
        limit #{page,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </select>

    <!-- 总量查询 -->
    <select id="total" resultType="java.lang.Integer">
        SELECT count(*) FROM ${prefix}_forum_comment
        <where>
            <if test ='null != typechoForumComment.id'>
                and `id` = #{typechoForumComment.id}
            </if>
            <if test ='null != typechoForumComment.uid'>
                and `uid` = #{typechoForumComment.uid}
            </if>
            <if test ='null != typechoForumComment.created'>
                and `created` = #{typechoForumComment.created}
            </if>
            <if test ='null != typechoForumComment.forumid'>
                and `forumid` = #{typechoForumComment.forumid}
            </if>
            <if test ='null != typechoForumComment.likes'>
                and `likes` = #{typechoForumComment.likes}
            </if>
            <if test ='null != typechoForumComment.text'>
                and `text` = #{typechoForumComment.text}
            </if>
            <if test ='null != typechoForumComment.parent'>
                and `parent` = #{typechoForumComment.parent}
            </if>
            <if test ='null != typechoForumComment.section'>
                and `section` = #{typechoForumComment.section}
            </if>
            <if test ='null != typechoForumComment.pic'>
                and `pic` = #{typechoForumComment.pic}
            </if>
            <if test ='null != typechoForumComment.status'>
                and `status` = #{typechoForumComment.status}
            </if>
            <if test ='null != searchKey'>
                and CONCAT(IFNULL(`text`, '')) LIKE CONCAT('%', #{searchKey}, '%')
            </if>
        </where>
    </select>
</mapper>